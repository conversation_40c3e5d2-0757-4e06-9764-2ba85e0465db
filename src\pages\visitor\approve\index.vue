<template>
  <view class="wrapper">
    <headle></headle>
    <list-component v-if="listData.length > 0"  :source="'1'" :title-list="titleListData" :list="listData"
      @select-item="handleDetailClick"></list-component>
    <view v-else class="empty-state">
      <text class="empty-text">当前无审批</text>
    </view>
  </view>
</template>
<script setup lang="ts">
import { ref, defineOptions, defineProps, defineEmits, defineModel } from 'vue'
import ListComponent from "@/pages/components/SComponents/ListComponent.vue";
import Headle from "@/pages/components/headle.vue";
import { getMainInfoListByTeacher, getMainInfoListByTeacherId } from "@/api/maininfo";
import cache from "@/utils/cache";
import { USER_INFO } from "@/enums/cacheEnums";
import { onReachBottom, onShow } from "@dcloudio/uni-app";
import { sexDict } from "@/api/generalDict";
const listData = ref([] as any[])
const titleListData = ref([
  {
    title: '姓名',
    type: 'text',
    code: 'visitorName'
  },
  {
    title: '性别',
    type: 'select',
    code: 'visitorGender',
    options: sexDict
  },
  {
    title: '手机号',
    type: 'text',
    code: 'visitorPhoneNumber'
  },
  {
    title: '来访时间',
    type: 'text',
    code: 'beginTime'
  },
  {
    title: '来访事由',
    type: 'text',
    code: 'purpose'
  },
] as any[])
const userInfo = ref<any>(cache.get(USER_INFO))
const queryParams = ref({
  // pageNum:1,
  // pageSize:4,
  jobNum: userInfo.value.userName
})
const getOrderList = async () => {
  await getMainInfoListByTeacher(queryParams.value).then((res: any) => {
    if (res) {
      listData.value = res.rows
      // if(queryParams.value.pageNum===1){
      //   listData.value=res.rows
      // }else {
      //   listData.value=[...listData.value,...res.rows]
      // }
      // areaTotal.value=res.total
    }
  })
}

const handleDetailClick = (e: any) => {
  uni.navigateTo({
    url: '/pages/visitor/generalPage/details?id=' + e.id + '&source=1',
    success(result) {
      // result.eventChannel.emit('detail',e)
      // result.eventChannel.emit('detailTitle',titleDataListDetails.value)
    },
  })
}
onShow(() => {
  // queryParams.value.pageNum=1
  getOrderList();
})
onReachBottom(() => {
  // if(areaTotal.value>(queryParams.value.pageNum*queryParams.value.pageSize)){
  //   queryParams.value.pageNum+=1
  //   getOrderList()
  // }
})
</script>
<style scoped lang="scss">
.wrapper {
  width: 100%;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
}

.empty-text {
  font-size: 16px;
  color: #999;
  text-align: center;
}
</style>
