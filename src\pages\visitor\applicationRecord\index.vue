<template>
  <view class="wrapper">
    <headle></headle>
    <search-component style="margin-top: 1rem" @search-data="handleSearchClick" source="1"></search-component>
    <list-component  :source="'2'" :title-list="titleListData" :list="listData" @select-item="handleDetailClick"></list-component>
  </view>
</template>
<script setup lang="ts">
import {ref,defineOptions,defineProps,defineEmits,defineModel} from 'vue'
import ListComponent from "@/pages/components/SComponents/ListComponent.vue";
import SearchComponent from "@/pages/components/SComponents/SearchComponent.vue";
import Headle from "@/pages/components/headle.vue";
import cache from "@/utils/cache";
import {USER_INFO} from "@/enums/cacheEnums";
import {getMainInfoListByTeacher, getMainInfoListByTeacherId} from "@/api/maininfo";
import {getApprovalWithMainInfoList} from "@/api/approval";
import {onReachBottom, onShow} from "@dcloudio/uni-app";
import {sexDict} from "@/api/generalDict";
import { getDicts } from "@/api/params";
const titleListData=ref([
  {
    title:'姓名',
    type:'text',
    code:'visitorName'
  },
  {
    title:'性别',
    type:'select',
    code:'visitorGender',
    options:sexDict
  },
  {
    title:'手机号',
    type:'text',
    code:'visitorPhoneNumber'
  },
  {
    title:'来访时间',
    type:'text',
    code:'beginTime'
  },
  {
    title:'来访事由',
    type:'text',
    code:'purpose'
  },
] as any[])
const listData=ref([])
const userInfo=ref<any>(cache.get(USER_INFO))
const queryParams=ref({
  pageNum:1,
  pageSize:10,
  createTime: null,
  approvalResult: null,
  visitStatus: null,
  campus: null,
  visitorName: null,
  approverId:userInfo.value.userName,
})
const getOrderList=async ()=>{
  // getMainInfoListByTeacherId
  await getApprovalWithMainInfoList(queryParams.value).then((res:any)=>{
    if(res){
      if(queryParams.value.pageNum===1){
        listData.value=[]
        listData.value=res.rows
        orderTotal.value=res.total
        // console.log(res, 'res');
      }else {
        listData.value=[...listData.value,...res.rows]
      }
      // 翻译来访事由
      const purposeList = ref([])
      getDicts("reason_for_visit").then(res => {
        if (res) {
          purposeList.value = res.data.map((m: any) => {
            return {
              text: m.dictLabel,
              value: m.dictValue
            }
          })
          listData.value.forEach(item => {
            item.purpose = purposeList.value.find(m => m.value === item.purpose).text
          })
        }
      })
    }
  })
}
onShow(()=>{
  queryParams.value.pageNum=1
  getOrderList();
})
const handleDetailClick=(e:any)=>{
  uni.navigateTo({
    url:'/pages/visitor/generalPage/details?id='+e.id+'&source=2',
    success(result) {
        // result.eventChannel.emit('detail',e)
        // result.eventChannel.emit('detailTitle',titleDataListDetails.value)
    },
  })
}
const handleSearchClick=(e:any)=>{
  queryParams.value.pageNum=1
  queryParams.value.createTime=e.createTime
  queryParams.value.approvalResult=e.approvalResult
  queryParams.value.visitStatus=e.visitStatus
  queryParams.value.campus=e.campus
  queryParams.value.visitorName=e.visitorName
  getOrderList();
}
const orderTotal=ref()
onReachBottom(()=>{
    if(orderTotal.value>(queryParams.value.pageNum*queryParams.value.pageSize)){
    queryParams.value.pageNum+=1
    getOrderList()
  }
})
</script>
<style scoped lang="scss">
.wrapper{
  width: 100%;
  .search-wrapper{

  }
}
</style>
