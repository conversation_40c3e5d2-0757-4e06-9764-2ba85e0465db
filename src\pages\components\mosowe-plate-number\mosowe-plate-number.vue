<!--
 @Description: 车牌输入器
 @Author: mosowe
 @Date:2025-04-21 09:15:36
-->
<template>
  <view
    class="mosowe-plate-number"
    :data-tag="currentTag"
    :style="{
      'z-index': zIndex
    }">
    <view
      class="input-wrap"
      :data-tag="currentTag"
      v-if="!popup && !custom">
      <template v-for="(_, index) in plateNumArr">
        <view
          :class="{
            'input-item': true,
            'input-item-cursor': focusIndex === index,
            'input-item-new-energy': index === 7
          }"
          :data-tag="currentTag"
          @click="getFocus(index)">
          {{ plateNumArr[index] }}
        </view>
        <view
          class="input-item-dot"
          :data-tag="currentTag"
          v-if="index === 1"></view>
      </template>
    </view>
    <view
      v-if="popup || custom"
      :data-tag="currentTag"
      @click="getFocus(0)">
      <slot></slot>
    </view>

    <view
      :class="{
        'keyboard-popup': true,
        'keyboard-popup-mask-transparent': !popup,
        'keyboard-popup-mask': popup
      }"
      :data-tag="currentTag"
      v-if="showKeyBoard">
      <view
        class="keyboard-popup-content"
        :data-tag="currentTag">
        <view class="top-wrap">
          <view
            class="close"
            @click="close">
            取消
          </view>
          <view
            class="confirm"
            @click="confirm">
            确定
          </view>
        </view>

        <view
          class="input-wrap input-wrap-popup"
          v-if="popup && !custom"
          :data-tag="currentTag">
          <template v-for="(_, index) in plateNumArr">
            <view
              :class="{
                'input-item': true,
                'input-item-cursor': focusIndex === index,
                'input-item-new-energy': index === 7
              }"
              :data-tag="currentTag"
              @click="getFocus(index)">
              {{ plateNumArr[index] }}
            </view>
            <view
              class="input-item-dot"
              :data-tag="currentTag"
              v-if="index === 1"></view>
          </template>
        </view>
        <!-- 首位汉字 -->
        <view
          class="keyboard-key-wrap"
          v-if="!showKeyBoardNext"
          :data-tag="currentTag">
          <view class="keyboard-key-content">
            <view
              class="key-item"
              :data-tag="currentTag"
              v-for="item in provinceList"
              @click="setValue(item)">
              {{ item }}
            </view>
            <view
              class="delete-content"
              :data-tag="currentTag">
              <view
                class="delete-wrap key-item-delete"
                :data-tag="currentTag"
                @click="deleteValue">
                <image
                  src="./delete.png"
                  :data-tag="currentTag"
                  class="delete-image" />
              </view>
            </view>
          </view>
        </view>
        <!-- 数字字母尾字 -->
        <view
          class="keyboard-key-wrap"
          :data-tag="currentTag"
          v-else>
          <view
            class="keyboard-key-content"
            :data-tag="currentTag">
            <view
              :class="{
                'key-item-letter': true,
                disabled: ([0, 1].includes(focusIndex) || (hasSpecialPlateEndChars && focusIndex > 6)) && !custom
              }"
              :data-tag="currentTag"
              v-for="item in numList"
              @click="setValue(item)">
              {{ item }}
            </view>
          </view>
          <view
            class="keyboard-key-content"
            :data-tag="currentTag">
            <view
              :class="{
                'key-item-letter': true,
                disabled: hasSpecialPlateEndChars && focusIndex > 6 && !custom
              }"
              :data-tag="currentTag"
              v-for="item in plateUpperCaseLetters"
              @click="setValue(item)">
              {{ item }}
            </view>
            <view
              :class="{
                'key-item-letter': true,
                disabled:
                  ([0, 1].includes(focusIndex) ||
                    focusIndex !== 6 ||
                    (hasSpecialPlateEndChars && focusIndex !== 6) ||
                    (hasSpecialPlateEndChars && focusIndex > 6)) &&
                  !custom
              }"
              :data-tag="currentTag"
              v-for="item in specialPlateEndChars"
              @click="setValue(item)">
              {{ item }}
            </view>
            <view
              class="delete-content"
              :data-tag="currentTag">
              <view
                class="delete-wrap key-item-letter-delete"
                :data-tag="currentTag"
                @click="deleteValue">
                <image
                  :data-tag="currentTag"
                  src="./delete.png"
                  class="delete-image" />
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
export default {
  props: {
    modelValue: {
      type: String,
      default: ''
    },
    // 弹框形式，结合插槽使用
    popup: {
      type: Boolean,
      default: false
    },
    // 自动获取焦点
    autoFocus: {
      type: Boolean,
      default: false
    },
    // 自定义，只需要键盘
    custom: {
      type: Boolean,
      default: false
    },
    // 自定义，字符串长度限制
    customLength: {
      type: Number,
      default: 7
    }
  },
  model: {
    prop: 'modelValue',
    event: 'update:modelValue'
  },
  data() {
    return {
      plateNumArr: ['', '', '', '', '', '', '', ''],
      focusIndex: -1,
      showKeyBoard: false,
      showKeyBoardNext: false,
      provinceList: [
        '京',
        '津',
        '沪',
        '渝',
        '冀',
        '豫',
        '云',
        '辽',
        '黑',
        '湘',
        '皖',
        '鲁',
        '新',
        '苏',
        '浙',
        '赣',
        '鄂',
        '桂',
        '甘',
        '晋',
        '蒙',
        '陕',
        '吉',
        '闽',
        '贵',
        '粤',
        '青',
        '川',
        '宁',
        '琼',
        '藏',
        '使',
        '领'
      ],
      plateUpperCaseLetters: [
        'A',
        'B',
        'C',
        'D',
        'E',
        'F',
        'G',
        'H',
        'J',
        'K',
        'L',
        'M',
        'N',
        'P',
        'R',
        'S',
        'T',
        'U',
        'V',
        'W',
        'X',
        'Y',
        'Z'
      ],
      numList: [0, 1, 2, 3, 4, 5, 6, 7, 8, 9],
      specialPlateEndChars: ['军', '警', '港', '澳'],
      currentTag: Math.random() + '_' + Date.now(),
      zIndex: 99
    };
  },
  watch: {
    focusIndex() {
      if (this.focusIndex === 0) {
        this.showKeyBoardNext = false;
      } else {
        this.showKeyBoardNext = true;
      }
    },
    modelValue() {
      if (this.custom) {
        this.focusIndex = this.modelValue.length < this.customLength ? this.modelValue.length : this.customLength;
      }
    }
  },
  computed: {
    hasSpecialPlateEndChars() {
      let r = false;
      for (let index = 0; index < this.plateNumArr.length; index++) {
        const item = this.plateNumArr[index];
        if (this.specialPlateEndChars.includes(item)) {
          r = true;
          break;
        }
      }
      return r;
    }
  },
  mounted() {
    this.setInitValue();
  },
  methods: {
    // 初始化值
    setInitValue() {
      this.plateNumArr = this.modelValue.split('');
      while (this.plateNumArr.length < 8) {
        this.plateNumArr.push('');
      }
      if (this.autoFocus) {
        this.getFocus(this.modelValue.length);
      }
    },
    // 获取焦点
    getFocus(index) {
      this.focusIndex = index;
      // this.plateNumArr[index] = '';
      this.showKeyBoard = true;
      this.showKeyBoardNext = index > 0;
      this.zIndex = 100;
      this.$emit('focus');
    },
    // 设置值
    setValue(item) {
      if (!this.custom) {
        // 2号位只能输入字母
        if ([0, 1].includes(this.focusIndex) && [...this.numList, ...this.specialPlateEndChars].includes(item)) {
          return;
        }
        // 非7号位不能输入尾部汉字
        if (
          (this.focusIndex !== 6 || (this.hasSpecialPlateEndChars && this.focusIndex !== 6)) &&
          [...this.specialPlateEndChars].includes(item)
        ) {
          return;
        }
        // 7号位已经输入尾部汉字，后续不能输入其他字符
        if (this.hasSpecialPlateEndChars && this.focusIndex > 6) {
          return;
        }
        this.plateNumArr[this.focusIndex] = item;
        if (this.focusIndex < 7 && this.focusIndex !== -1) {
          this.focusIndex += 1;
        } else {
          this.focusIndex = -1;
        }
      } else {
        this.$emit('update:modelValue', this.modelValue + (this.modelValue.length < this.customLength ? item : ''));
      }
    },
    // 关闭
    close() {
      if (!this.custom) {
        this.setInitValue();
        this.focusIndex = -1;
        this.showKeyBoard = false;
        this.zIndex = 99;
        this.$emit('blur');
      } else {
        this.$emit('close', (bool) => {
          this.zIndex = 99;
          this.showKeyBoard = !bool;
          this.$emit('blur');
        });
      }
    },
    // 确定
    confirm() {
      if (!this.custom) {
        if (!this.validatePlateNumberArray(this.plateNumArr)) {
          uni.showToast({
            title: '请输入完整的车牌号',
            icon: 'none'
          });
          return;
        }
        if (!this.isDiplomaticPlate(this.plateNumArr.join(''))) {
          uni.showToast({
            title: '请输入正确的车牌号',
            icon: 'none'
          });
          return;
        }
        this.showKeyBoard = false;
        this.focusIndex = -1;
        this.$emit('update:modelValue', this.plateNumArr.join(''));
        this.zIndex = 99;
        this.$emit('blur');
      } else {
        this.$emit('confirm', (bool) => {
          this.showKeyBoard = !bool;
          this.zIndex = 99;
          this.$emit('blur');
        });
      }
    },
    validatePlateNumberArray(arr) {
      if (arr.join('').length < 6) {
        return false;
      }
      const e = ['', undefined, null];
      // 检查数组中非末尾的元素是否为空
      for (let i = 0; i < arr.length - 1; i++) {
        if (i < 7 && e.includes(arr[i]) && arr[i + 1]) {
          return false; // 非末尾元素为空，返回 false
        }
      }
      return true; // 所有非末尾元素都不为空，返回 true
    },
    // 删除
    deleteValue() {
      if (!this.custom) {
        // 从数组末尾开始查找最后一个有值的项
        for (let i = this.plateNumArr.length - 1; i >= 0; i--) {
          if (!['', undefined, null].includes(this.plateNumArr[i])) {
            this.plateNumArr[i] = '';
            this.focusIndex = i;
            break;
          }
        }
      } else {
        let str = this.modelValue;
        this.$emit('update:modelValue', str.substring(0, str.length - 1));
      }
    },
    isDiplomaticPlate(plate) {
      const plateRegex =
        /^(?:[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-HJ-NP-Z][A-HJ-NP-Z0-9]{4,5}[A-HJ-NP-Z0-9挂学警港澳]|使[0-9]{6}|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-HJ-NP-Z][0-9]{4}[领]|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼]Z[0-9]{4}[港澳]|WJ[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][0-9]{4}[警]|[海空北沈兰济南广成][A-Z][0-9]{4}|[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼][A-HJ-NP-Z][0-9]{5})$/;
      return plateRegex.test(plate);
    }
  }
};
</script>

<style scoped lang="scss">
.mosowe-plate-number {
  width: 100%;
  position: relative;
  z-index: 99;
  .input-wrap {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    z-index: 2;
    &.input-wrap-popup {
      padding: 24rpx 0;
    }
  }
  .input-item {
    width: 70rpx;
    height: 70rpx;
    line-height: 70rpx;
    text-align: center;
    font-size: 40rpx;
    color: #000000;
    border: 1px solid #ccc;
    transform: rotate(360deg);
    border-radius: 8rpx;
    overflow: visible;
    position: relative;
    &.input-item-cursor::before {
      content: '';
      display: block;
      width: 80%;
      height: 2px;
      border-radius: 2px;
      background-color: $uni-color-primary;
      position: absolute;
      left: 50%;
      bottom: 4rpx;
      transform: translateX(-50%);
    }
    &.input-item-new-energy {
      border-color: #67c23a;
      &::after {
        content: '新能源';
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%) translateY(-50%) scale(0.6);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 16rpx;
        line-height: 16rpx;
        color: #fff;
        background-color: #67c23a;
        border-radius: 50rpx;
        width: 80rpx;
        height: 30rpx;
        white-space: nowrap;
      }
    }
  }
  .input-item-dot {
    width: 10rpx;
    height: 10rpx;
    border-radius: 50%;
    background-color: #000;
    transform: rotate(360deg);
  }
}
.keyboard-popup {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  box-sizing: border-box;
  &.keyboard-popup-mask-transparent::after {
    content: '';
    display: block;
    width: 100vw;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0);
    position: fixed;
    left: 0;
    top: 0;
  }
  &.keyboard-popup-mask::after {
    content: '';
    display: block;
    width: 100vw;
    height: 100vh;
    background-color: rgba($color: #000000, $alpha: 0.5);
    position: fixed;
    left: 0;
    top: 0;
  }
  .keyboard-popup-content {
    position: relative;
    z-index: 98;
    background-color: #ffffff;
    box-shadow: 0 0 10rpx rgba($color: #000000, $alpha: 0.1);
    padding: 0 16rpx 32rpx 16rpx;
  }
  .top-wrap {
    padding: 12rpx 8rpx;
    font-size: 32rpx;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .close {
      color: $uni-text-color-grey;
    }
    .confirm {
      color: $uni-color-primary;
    }
  }
  .keyboard-key-wrap {
    padding: 12rpx;
    background-color: #eee;
    width: 712rpx;
    box-sizing: border-box;
    margin: 0 auto;
  }
  .keyboard-key-content {
    width: 688rpx;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    justify-content: space-between;
    & + .keyboard-key-content {
      margin-top: 20rpx;
    }
    .delete-content {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
    .delete-wrap {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 0;
      background-color: #fff;
      height: 86rpx;
      border-radius: 8rpx;
      .delete-image {
        width: 44rpx;
        height: 44rpx;
      }
    }
    .key-item-delete {
      max-width: 192rpx;
    }
    .key-item-letter-delete {
      max-width: 198rpx;
    }
    .key-item {
      flex: none;
      width: 86rpx;
      height: 86rpx;
      line-height: 86rpx;
      margin: 7rpx calc((688rpx - 86rpx * 7) / 6) 7rpx 0;
      background-color: #fff;
      border-radius: 8rpx;
      text-align: center;
      font-size: 40rpx;
      color: #000;
      &:nth-child(7n) {
        margin-right: 0;
      }
    }
    .key-item-letter {
      flex: none;
      width: 58rpx;
      height: 86rpx;
      line-height: 86rpx;
      margin: 6rpx 12rpx 6rpx 0;
      background-color: #fff;
      border-radius: 8rpx;
      text-align: center;
      font-size: 40rpx;
      color: #000;
      &:nth-child(10n) {
        margin-right: 0;
      }
      &.disabled {
        opacity: 0.5;
        color: #333333;
      }
    }
    .space {
      width: 280rpx;
      height: 80rpx;
    }
  }
}
</style>
