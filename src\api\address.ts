import request from '@/utils/request/index'

export interface AddressParams{
    /** 主键, 自动增长列 */
    id:number
    /** 机构编号 */
    orgcode:string
    /** 学工号 */
    jobNum:string
    /** 教师姓名 */
    teacherName:string
    /** 校区id */
    campusId:number
    /** 校区 */
    campus:string
    /** 楼宇id */
    buildingId:number
    /** 楼宇 */
    building:string
    /** 房间号 */
    roomNo:string
    /** 是否默认 0-不是; 1-是 */
    isDefault:string,
    //单位id
    svaReservedText1:string,
    //单位
    svaReservedText2:string
}
export function getAddressList(data:any){
    return request.get({
        url:'visitor/visitaddress/list',
        data:data
    })
}

export function addAddressList(data:AddressParams){
    return request.post({
        url:'/visitor/visitaddress',
        data:data
    })
}
export function setDefaultAddress(id:number){
    return request.get({
        url:'visitor/visitaddress/setdefault?id='+id
    })
}
export function getDeptList(data:any){
    return request.get({
        url:'system/dept/list',
        data:data
    })
}
// 查询教师访问地址详细
export function getVisitaddress(id:number) {
    return request.get({
        url: '/visitor/visitaddress/' + id
    })
}

// 修改教师访问地址
export function updateVisitaddress(data:any) {
    return request.post({
        url: '/visitor/visitaddress/update',
        data: data
    })
}

// 删除教师访问地址
export function delVisitaddress(id:number) {
    return request.post({
        url: '/visitor/visitaddress/' + id
    })
}
