import request from '@/utils/request/index'

export interface AreaParams{
    /** 主键 */
    id:number
    /** 区域名称 */
    name:string
    /** 区域编号 */
    code:number
    //父id
    parentId:number
    /** 父级路径 */
    parentIds:string
    /** 负责人id */
    chiefId:number
    /** 负责人姓名 */
    chiefName:string
}
export function getAreaList(){
    return request.get({
        url:'visitor/areamanage/list',
    })
}
