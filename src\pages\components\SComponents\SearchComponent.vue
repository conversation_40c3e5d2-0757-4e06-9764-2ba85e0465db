<template>
  <view class="wrapper">
    <view class="search-wrapper" v-if="props.source=='1'">
      <view class="search-item">
        <uni-datetime-picker  type="date" placeholder="申请日期" :clear-icon="true" v-model="searchData.createTime"/>
      </view>
      <view class="search-item">
        <uni-data-select label="审批状态" placeholder="请选择审批状态" v-model="searchData.approvalResult" :localdata="approveStatusList" />
      </view>
<!--      <view class="search-item">-->
<!--        <uni-data-select label="访问状态" placeholder="请选择访问状态" v-model="searchData.visitStatus" :localdata="visitStatusList" />-->
<!--      </view>-->
      <view class="search-item" style="border: 1px solid #e5e5e5;display: flex;align-items: center;padding-left: 0.3rem">
        <input style="font-size: 14px"  placeholder="请输入校区" v-model="searchData.campus" />
      </view>
      <view class="search-item" style="height:35px;border: 1px solid #e5e5e5;display: flex;align-items: center;padding-left: 0.3rem">
        <input style="font-size: 14px"  placeholder="请输入姓名" v-model="searchData.visitorName" />
      </view>
    </view>
    <view class="search-wrapper" v-if="props.source=='2'">
      <view class="search-item">
        <uni-datetime-picker type="date" placeholder="日期" :clear-icon="false" v-model="searchData2.inDate"/>
      </view>
      <view class="search-item" style="height:35px;border: 1px solid #e5e5e5;display: flex;align-items: center;padding-left: 0.3rem">
        <input style="font-size: 14px"  placeholder="请输入姓名" v-model="searchData2.visitorName" />
      </view>
    </view>
    <view class="search-wrapper">
      <view class="search-item" >
        <button type="default" @click="handleReset">重置</button>
      </view>
      <view class="search-item" >
        <button type="primary" @click="handleSearch">查询</button>
      </view>
    </view>
  </view>

</template>
<script setup lang="ts">
import {ref, reactive, onMounted} from 'vue'
const props = defineProps({
  source: {
    type: String,
    default: ''
  }
})
const searchData = reactive({
  createTime: null,
  approvalResult: null,
  visitStatus: null,
  campus: null,
  visitorName: null
})
const searchData2 = reactive({
  inDate: null,
  visitorName: null
})
import uniDatetimePicker from "@/pages/components/uni-datetime-picker/uni-datetime-picker.vue";
import UniDataSelect from "@/pages/components/uni-data-select/uni-data-select.vue";
import {approveResultList} from "@/api/generalDict";
const approveStatusList=ref(approveResultList)
const visitStatusList=ref([
  { value: '0', text: "已访问" },
  { value: '1', text: "已离开" },
])
const emit = defineEmits(['searchData'])
const handleSearch=()=>{
  if(props.source=='1'){
    emit('searchData',searchData)
  }else if(props.source=='2'){
    emit('searchData',searchData2)
  }

}
const handleReset=()=>{
  searchData.createTime=null
  searchData.approvalResult=null
  searchData.visitStatus=null
  searchData.campus=null
  searchData.visitorName=null
  searchData2.inDate=null
  searchData2.visitorName=null
  emit('searchData',searchData)
}
</script>
<style scoped lang="scss">
.wrapper {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;

  .search-wrapper {
    width: 90%;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between ;
    .search-item {
      width: 48%;
      margin-top: 0.5rem;
    }
  }

}
</style>
