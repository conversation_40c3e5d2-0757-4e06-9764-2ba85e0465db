import { defineConfig } from "vite";
import uni from "@dcloudio/vite-plugin-uni";

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [uni()],
  server: {
    port: 5177,
    host: true,
    open: false,
    proxy: {
      // https://cn.vitejs.dev/config/#server-proxy
      '/dev-api': {
        target: 'http://localhost:5414',
        changeOrigin: true,
        rewrite: (p) => p.replace(/^\/dev-api/, '')
      },
    }
  }
});
