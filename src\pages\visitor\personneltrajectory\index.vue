<template>
<view class="wrapper-personnel-trajectory">
  <view class="content-wrapper">
    <uni-popup :mask-click="false" v-if="false" style="z-index: 99999" ref="popup" type="bottom" border-radius="10px 10px 0 0">
      <view class="popup-wrapper">
        <view class="search-i-wrapper" >
<!--          <view class="title-wrapper">-->
<!--            选择人员-->
<!--          </view>-->
<!--          <view class="search-i-content">-->
<!--            <view class="input-item-w" >-->

<!--              <view class="input-item-t">-->
<!--                姓名:-->
<!--              </view>-->
<!--              <view class="input-item-c">-->
<!--                <input style="border:1px solid #c0c0c073;height: 1.5rem" class="uni-input" v-model="dataP.data.name"  placeholder="请输入姓名" />-->
<!--              </view>-->
<!--            </view>-->
<!--            <view class="input-item-w" >-->

<!--              <view class="input-item-t">-->
<!--                学号:-->
<!--              </view>-->
<!--              <view class="input-item-c">-->
<!--                <input style="border:1px solid #c0c0c073;height: 1.5rem" class="uni-input" v-model="dataP.data.ucode"  placeholder="请输入学号" />-->
<!--              </view>-->
<!--            </view>-->
<!--          </view>-->
          <view class="search-i">
            <view class="d-w">
              <uni-datetime-picker type="date" v-model="happenTime" >{{ happenTime?happenTime:'请选择日期' }}</uni-datetime-picker>
            </view>
            <view class="s-w">
              <view class="i-w">
                <input style="width:100%;height: 1.5rem" class="uni-input" @input="handleChangeSearchI" v-model="dataP.data.name"   placeholder="请输入学号或人员编号" />
              </view>
              <view class="c-w" v-if="dataP.data.name" @click="handleCloseC">
                <uni-icons type="close" size="22"></uni-icons>
              </view>
              <view class="b-w">
                <button size="mini" @click="handleClickSearchB">确定</button>
              </view>
            </view>
            <scroll-view v-if="searchPromptList.length>0"  :scroll-top="0" scroll-y="true" class="search-prompt-wrapper" :style="{height:scrollheight+'rem'}" @scrolltolower="upper">
              <view class="item-search-list" v-for="item in searchPromptList" @click="handleClickSearchSelect(item)">
                <view class="item-s">{{item.name}}</view>
                <view class="item-s-1">
                  {{item.jobNum}}
                  {{item.type?typeList.filter(e=>e.value===item.type)?typeList.filter(e=>e.value===item.type)[0].label:'':''}}
                </view>
              </view>
            </scroll-view>
          </view>
        </view>
      </view>

    </uni-popup>
    <view v-if="false" class="list-wrapper">

    </view>
    <view  v-if="true" class="map-wrapper">
      <line-map ref="mapRef" v-model:select-p-i="selectEersinnelInfo.data" v-model:select-d="happenTime" v-model:m-o-t="mOrT">

      </line-map>
    </view>
  </view>
  <view class="search-wrapper" >
<!--    <view class="search-icon-wrapper" @click="handleClickSearchButton">-->
<!--      <image :src="searchIcon"/>-->
<!--    </view>-->
    <view class="button-wrapper">
      <view class="left-w" v-if="false" @click="handleClickSearchButton">
        搜索
      </view>
      <view @click="cutMapOrTable" class="right-w">
        {{mOrT==='m'?'列表':'地图'}}
      </view>
    </view>
  </view>
</view>
</template>
<script setup lang="ts">
import lineMap from '@/pages/components/LineMap/index.vue'
import uniDatetimePicker from "@/pages/components/uni-datetime-picker/uni-datetime-picker.vue";
import {ref, reactive, onMounted} from "vue";
import {onLoad} from "@dcloudio/uni-app";
import uniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import {timestampToTime} from "@/utils/disposeDate";
import uniIcons from "@/pages/components/uni-icons/uni-icons.vue";
const popup = ref()
const menuId=ref()
onLoad((e:any)=>{
  menuId.value=e.menuId;
  // getTrajectoryList()

})
onMounted(()=>{
  popup.value?.open('bottonm')
})
const getTrajectoryList=()=>{
  // getPersonnelTrajectoryList({menuId:menuId.value}).then(res=>{
  //   console.log(res)
  // })
}
const handleClickSearchButton=()=>{
  popup.value?.open('bottom')
}
const dataP=reactive({
  data:{
    name:'',
    pageNum:1,
    pageSize:10,
    // ucode:''
  }
})
const happenTime=ref(timestampToTime(new Date(),'YYYY-MM-DD'))
const totalS=ref(0)
const handleChangeSearchI=(e:any)=>{
  console.log()
  if(e.detail.value){
    dataP.data.name=e.detail.value
    handleGetPersonnelTraInfo()
  }else {
    searchPromptList.value=[]
  }

}
const searchPromptList=ref<any[]>([])
const handleGetPersonnelTraInfo=()=>{
  // getPersonnelTraInfo(dataP.data).then(res=>{
  //   searchPromptList.value=res.data.rows
  //   totalS.value=res.data.total
  //   if(res.data.rows.length*2.4>18){
  //     scrollheight.value=18
  //   }else {
  //     scrollheight.value=res.data.rows.length*2.4
  //   }
  // })
}
const mapRef=ref()
const handleClickSearchB=()=>{
  if(selectEersinnelInfo.data.jobNum){
    // getPersonnelTrajectoryList({menuId:menuId.value,userCode:selectEersinnelInfo.data.jobNum,happenTime:happenTime.value}).then(res=>{
    //   console.log(res)
    // })
    // mapRef.value.getLocalList()
    mapRef.value.showPoly()
    popup.value.close()
  }else {
    uni.showToast({
      title:'请选择学生',
      icon:'none'
    })
  }

}
const upper=()=>{
  if(totalS.value<dataP.data.pageNum*dataP.data.pageSize){

  }else {
    dataP.data.pageNum+=1
    // getPersonnelTraInfo(dataP.data).then(res=>{
    //   searchPromptList.value=searchPromptList.value.concat(res.data.rows)
    // })
  }

}
const typeList=ref([
  {
    value:'1',
    label:'老师'
  },
  {
    value:'2',
    label:'学生 '
  },
  {
    value:'3',
    label:'第三方人员'
  },
  {
    value:'4',
    label:'黑名单人员'
  }
])
const scrollheight=ref(18)
const selectEersinnelInfo=reactive({
  data:{} as any
})
const handleClickSearchSelect=(item:any)=>{
  selectEersinnelInfo.data=item
  dataP.data.name=item.name+'    '+item.jobNum+'    '+(item.type?typeList.value.filter(e=>e.value===item.type)?typeList.value.filter(e=>e.value===item.type)[0].label:'':'')
  console.log(dataP.data.name)
  searchPromptList.value=[]
}
const handleCloseC=()=>{
  dataP.data.name=''
  searchPromptList.value=[]
  selectEersinnelInfo.data={}
}
const mOrT=ref('m')
const cutMapOrTable=()=>{
  if(mOrT.value==='m'){
    mOrT.value='t'
  }else {
    mOrT.value='m'
    mapRef.value.initMap()
  }
}
</script>
<style scoped lang="scss">
.wrapper-personnel-trajectory{
  .search-wrapper {
    position: fixed;
    bottom: 60px;
    right: 0px;
    .button-wrapper{
      display: flex;
      color: white;
      .left-w{
        width: 3rem;
        background-color: rgba(148, 236, 204, 0.55);
        height: 1.5rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .right-w{
        width: 3rem;
        background-color: rgba(90, 172, 230, 0.58);
        display: flex;
        align-items: center;
        height: 1.5rem;
        justify-content: center;
      }
    }
    .search-icon-wrapper {
      width: 3rem;
      height: 3rem;
      border-radius: 50%;
      position: fixed;
      bottom: 3rem;
      right: 3rem;

      image {
        width: 100%;
        height: 100%;
      }
    }


  }
  .content-wrapper{
    .popup-wrapper{
      width: 100%;
      display: flex;
      justify-content: center;
      margin-bottom:5rem;
      .search-i-wrapper{
        background-color: white;
        width: 20rem;
        padding-right: 1rem;
        padding-left: 1rem;
        padding-bottom: 1.5rem;
        .search-i{
          display: flex;
          align-items: center;
          padding-top: 2rem;
          flex-direction: column;
          .d-w{
            width: 100%;
            margin-bottom: 0.5rem;

            border-bottom:1px solid #c0c0c073;
            height: 1.5rem;
            color: gray;
            font: inherit;
          }
          .s-w{
            display: flex;
            border-bottom:1px solid #c0c0c073;
            width: 100%;
            .i-w{
              width: 80%;
            }
            .c-w{
              display: flex;
              justify-content: center;
              align-items: center;
              position: relative;
              height: 1.5rem;
              background-color: rgba(192, 192, 192, 0);
            }
            .b-w{
              width: 20%;
            }
          }
          .search-prompt-wrapper{
            display: flex;
            .item-search-list{
              border-bottom:1px solid #c0c0c073;
              display: flex;
              flex-direction: column;
              .item-s{
                width: 100%;
                display: flex;

              }
              .item-s-1{

              }
            }
          }
        }
        .title-wrapper{
          line-height: 2rem;
          display: flex;
          justify-content: center;
          font-weight: 600;
          font-size: 18px;
          width: 100%;
        }
        .search-i-content{
          margin-bottom: 1rem;
          padding: 0.6rem 1rem 0rem 1rem;
          //padding: 0.5rem;
          .input-item-w{
            display: flex;
            align-items: center;
            padding-right: 0.5rem;
            padding-left: 0.5rem;
            padding-bottom: 0.5rem;
            .input-item-t{
              width:20%;
            }
            .input-item-c{
              width: 80%;
            }
          }
        }
      }
    }

  }
}

</style>
