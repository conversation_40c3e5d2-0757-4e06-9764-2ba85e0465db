import { TOKEN_KEY,USER_INFO } from '@/enums/cacheEnums'
import cache from '@/utils/cache'
import { defineStore } from 'pinia'
import {getInfo} from "@/api/login";

interface UserSate {
    userInfo: Record<string, any>,
    token: string | null,
    temToken: string | null,
    role:string |null,
}
export const useUserStore = defineStore("userStore",{
    state: (): UserSate => ({
        userInfo: {},
        token: cache.get(TOKEN_KEY) || null,
        temToken: null,
        role:null,
    }
    ),
    getters: {
        isLogin: (state) => !!state.token
    },
    actions: {
        async getUser() {
            // const data = await getUserCenter({
            //     token: this.token || this.temToken
            // })
            // this.userInfo = data.user
            // cache.set(USER_INFO,data.user)
        },
        async getInfo(){
           await getInfo().then((res:any)=>{
               if(res){
                   this.userInfo=res.user
                   cache.set(USER_INFO,res.user)
               }
           })
        },
        login(token: string) {
            this.token = token
            cache.set(TOKEN_KEY, token)
        },
        logout() {
            this.token = ''
            this.userInfo = {}
            cache.remove(TOKEN_KEY)
            cache.remove(USER_INFO)
        }
    }
})
