import request from '@/utils/request/index'
export interface MainInfoParams{
    /** 主键, 自动增长列 */
    id:number
    /** 机构编号 */
    orgcode:string
    /** 主键 */
    visitId:string
    /** 被访老师学工号 */
    jobNum:string,
    /** 被访老师姓名 */
    teacherName:string,
    /** 校区id */
    campusId:string
    /** 被访问校区 */
    campus:string,
    /** 楼宇id */
    buildingId:string,
    /** 被访问楼宇 */
    building:string,
    /** 被访问房间号 */
    roomNo:string,
    /** 访客姓名 */
    visitorName:string,
    /** 访客姓别 */
    visitorGender:string
    /** 访客身份证号 */
    visitorIdNumber:string,
    /** 访客电话 */
    visitorPhoneNumber:string,
    /** 访客车辆信息 */
    visitorCarInfor:string,
    /** 访客工作单位 */
    visitorWorkplace:string,
    /** 来访目的 */
    purpose:string,
    /** 开始时间 yyyy-MM-dd*/
    beginTime:string,
    /** 结束时间 yyyy-MM-dd*/
    endTime:string,
    /** 是否有其它同行人 */
    isOtherVisitors:string,
    /** 是否有其它同行车辆 */
    isOtherCars:string,
    /** 访客数量 */
    visitorCount:number,
    /** 车辆数量 */
    carCount:number,
    /** 审批结果编码 待审核0 已经开始审核1 通过2 未通过3 */
    approvalResult:string,
    authCode:string,
    /** 是否下发到设备 0-未下发 1-已下发 */
    isDtd:string,
    /** 下发到设备时间 yyyy-MM-dd*/
    dtdTime:string
    /** 实际开始时间 yyyy-MM-dd*/
    actualBeginTime:string,
    /** 实际结束时间 yyyy-MM-dd*/
    actualEndTime:string,
    pic:string[],
    information:string[],
    vCarInfoS:CarParams[],
    vPersonInfoS:PersonParams[]
}
export interface PersonParams{
    /** 主键, 自动增长列 */
    id:number,
    /** 机构编号 */
    orgcode:string,
    /** 关联访问ID */
    visitId:string,
    /** 访客姓名 */
    visitorName:string,
    /** 访客姓别 */
    visitorGender:string,
    /** 类型 0-主访客 1-同行人 */
    type:string,
    /** 访客身份证号 */
    visitorIdNumber:string,
    /** 访客电话 */
    visitorPhoneNumber:string,
    /** 访客工作单位 */
    visitorWorkplace:string,
    /** 开始时间 yyyy-MM-dd*/
    beginTime:string,
    /** 结束时间 yyyy-MM-dd*/
    endTime:string,
    /** 是否下发到设备 0-未下发 1-已下发 */
    isDtd:string,
    /** 人脸识别路径1 */
    pic:string[],
    information:string[],
}
export interface CarParams{
    /** 主键, 自动增长列 */
    id:number,
    /** 机构编号 */
    orgcode:string,
    /** 关联访问ID */
    visitId:string,
    /** 访客车辆信息 */
    visitorCarInfor:string,
    /** 类型 0-主车辆 1-同行车辆 */
    type:string,
    /** 开始时间 yyyy-MM-dd*/
    beginTime:string,
    /** 结束时间 yyyy-MM-dd*/
    endTime:string
}
export interface ApprovalResultParams{
    /** 主键, 自动增长列.*/
    id:number
    /** 机构编号 */
     orgcode:string
    /** 访问申请编码, 该值和v_main_info中的visit_id多对一的关系 */
    visitId:string
    /** 审批步骤号 */
    approvalStepNo:number
    /** 审批人编号 */
    approverId:string
    /** 审批人姓名 */
    approverName:string
    /** 审批结果编码   待审核0   通过2  未通过3  若该字段为空, 说明还没有到该人员审批 */
    approvalResult:string
    /** 审批意见 */
    approvalOpinion:string
    /** 审批时间 */
    approveTime:string
}
export interface ApprovalParams{
    id:number
    jobNum:string
    approvalResult:string
    approvalOpinion:string
}


export function getMainInfoListByTeacherId(data:any) {
  return request.get({
      url:'/visitor/maininfo/list',
      data
  })
}
export function getMainInfoListByTeacher(data:any) {
  return request.get({
      url:'/visitor/maininfo/listbyids',
      data
  })
}

export function getMainInfoDetailsById(id:number) {
  return request.get({
      url:'/visitor/maininfo/getorderinfo?id='+id
  })
}
export function getMainInfoDetailsByIdAuth(id:number) {
  return request.get({
      url:'/visitor/maininfo/getorderinfoauth?id='+id
  })
}

export function addMainInfo(params:MainInfoParams) {
  return request.post({
      url:'/visitor/maininfo/addmain',
      data:params
  })
}
export function approvalOrder(data:ApprovalParams) {
  return request.post({
      url:'/visitor/maininfo/approve',
      data
  })
}
export function getMainInfoListByJXZ(data:any) {
    return request.get({
        url:'/visitor/maininfo/listjxz',
        data
    })
}
export function getMainInfoListByYJS(data:any) {
    return request.get({
        url:'/visitor/maininfo/listyjs',
        data
    })
}
