<template>
  <view class="wrapper"   >
    <headle></headle>
    <wrapper-component style="margin-top: 10rem">
      <view style="width:100%;margin-bottom:1rem;display: flex;justify-content: center;align-items:center;font-size: 24px;font-weight: 600">
        登录
      </view>
      <uni-forms ref="form" :modelValue="loginForm" :rules="rules">
        <uni-forms-item :label="'账号'" :name="'账号'" label-width="6rem" >
          <uni-easyinput style="width: 14rem;" v-model="loginForm.username" :placeholder="'请输入用户名'" />
        </uni-forms-item>
        <uni-forms-item :label="'密码'" :name="'密码'" label-width="6rem" >
          <uni-easyinput style="width: 14rem;" v-model="loginForm.password" type="password" :placeholder="'请输入密码'" />
        </uni-forms-item>
        <uni-forms-item :label="'验证码'" :name="'验证码'" label-width="6rem" >
          <view style="display: flex;">
            <uni-easyinput style="width:7rem" v-model="loginForm.code"  :placeholder="'请输入验证码'" />
            <image @click="getCode" :src="codeUrl" style="width: 7rem;height: 2rem;margin-left: 0.5rem"></image>
          </view>

        </uni-forms-item>
      </uni-forms>
      <button type="primary"  @click="handelSub">
        提交
      </button>
    </wrapper-component>
  </view>
</template>

<script setup lang="ts">
import mainWrapper from '@/pages/components/mainWrapper.vue';
import {getCodeImg, login} from "@/api/login"
import {ref, reactive} from "vue";
import {decrypt, encrypt} from "@/utils/jsencrypt";
import type {doLoginParams} from "@/api/casLogin";
import {loginByStudentCode, loginByTeacherCode} from "@/api/casLogin";
import cache from "@/utils/cache";
import {TOKEN_KEY} from "@/enums/cacheEnums";
import Headle from "@/pages/components/headle.vue";
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import uniEasyinput from "@/pages/components/uni-easyinput/uni-easyinput.vue";
import uniForms from "@/pages/components/uni-forms/uni-forms.vue";
import uniFormsItem from "@/pages/components/uni-forms-item/uni-forms-item.vue";
import {useUserStore} from "@/stores/user";
const userStore =useUserStore()
const loginParams = reactive({
  data: {} as doLoginParams
})
const jLoginParams = reactive({
  data: {} as doLoginParams
})
const loginForm = ref({
  username: "201513284",
  password: "123456",
  rememberMe: false,
  code: "",
  uuid: ""
})
const rules = ref<any>({});
const captchaEnabled = ref(true)
const codeUrl = ref('')

function getCode() {
  getCodeImg().then((res: any) => {
    captchaEnabled.value = res.captchaEnabled === undefined ? true : res.captchaEnabled
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.img
      loginForm.value.uuid = res.uuid
    }
  })
}

getCode()
const doLogin = async () => {
  let url = location.href
  let subUrl = url.indexOf('?') > 1 ? url.substring(url.indexOf('?') + 1).split('&') : []
  subUrl.forEach(e => {
    let key = e.substring(0, e.indexOf('='))
    let value = e.substring(e.indexOf('=') + 1)
    //@ts-ignore
    loginParams.data[key] = value
    //@ts-ignore
    jLoginParams.data[key] = decrypt(decodeURIComponent(atob(decodeURIComponent(value))))
  })
  await uni.showLoading({title: '登录中'});
  try {
    if (jLoginParams.data.idType && jLoginParams.data.idType === '1') {
      await loginByStudentCode({
        username: jLoginParams.data.loginName,
        secretKey: decodeURIComponent(loginParams.data.loginName),
        thisTime: '0'
      }).then(res => {
        console.log(res.code)
        if (res.code == 200) {
          cache.set(TOKEN_KEY, res.token.access_token)
          cache.set('idType', jLoginParams.data.idType)
          uni.reLaunch({
            url: '/pages/firstIndex/index'
          })
        }
      }).catch(err => {
        console.log(err)
      })
    } else if (jLoginParams.data.idType && jLoginParams.data.idType === '3') {
      await loginByTeacherCode({
        username: jLoginParams.data.loginName,
        secretKey: decodeURIComponent(loginParams.data.loginName),
        thisTime: '0'
      }).then(res => {
        if (res.code == 200) {
          cache.set(TOKEN_KEY, res.token.access_token)
          cache.set('idType', jLoginParams.data.idType)
          uni.reLaunch({
            url: '/pages/firstIndex/index'
          })
        }
      }).catch(err => {

      })
    }
  } catch (e) {
    console.log('报错')
    console.log(e)
  } finally {
    uni.hideLoading()
  }
  uni.hideLoading()
}
// doLogin()

const handelSub = async () => {
  await login(loginForm.value).then(async res=>{
    if(res){
      await userStore.login(res.token)
      await userStore.getInfo()
      uni.reLaunch({
        url: '/pages/index/index'
      })
    }
  })

}

</script>

<style scoped lang="scss">
.content-wrapper {
  width: 100%;
  display: flex;
  padding: 0rem 1rem;

  .content {
    display: flex;
    margin-top: 12rem;
    width: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    //color: white;
    input {
      //background-color: white;
      width: 80%;
      margin-bottom: 1rem;
      height: 2rem;
      padding-left: 1rem;
      border-radius: 1rem;
    }

    button {
      width: 50%;
      border-radius: 1.5rem;
      margin-top: 1rem;
    }

    .content-title {
      font-weight: 600;
      font-size: 32px;
      margin-bottom: 2rem;
    }
  }
}
</style>
