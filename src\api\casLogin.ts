import request from '@/utils/request/index'

export interface doLoginParams {
    idType:string,
    loginName:string,
    thisTime:string
}
// 学生访问
export function loginByStudentCode(data:any) {
    return request.post({
        url: '/qywechat/loginByStudentCode',
        data
    },{
        isTransformResponse:false,
        withToken:false
    })
}
// 老师访问
export function loginByTeacherCode(data:any) {
    return request.post({
        url: '/qywechat/loginByTeacherCode',
        data
    },{
        isTransformResponse:false,
        withToken:false
    })
}
