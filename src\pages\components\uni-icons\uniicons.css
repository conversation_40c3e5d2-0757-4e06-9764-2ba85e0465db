
.uniui-cart-filled:before {
  content: "\e6d0";
}

.uniui-gift-filled:before {
  content: "\e6c4";
}

.uniui-color:before {
  content: "\e6cf";
}

.uniui-wallet:before {
  content: "\e6b1";
}

.uniui-settings-filled:before {
  content: "\e6ce";
}

.uniui-auth-filled:before {
  content: "\e6cc";
}

.uniui-shop-filled:before {
  content: "\e6cd";
}

.uniui-staff-filled:before {
  content: "\e6cb";
}

.uniui-vip-filled:before {
  content: "\e6c6";
}

.uniui-plus-filled:before {
  content: "\e6c7";
}

.uniui-folder-add-filled:before {
  content: "\e6c8";
}

.uniui-color-filled:before {
  content: "\e6c9";
}

.uniui-tune-filled:before {
  content: "\e6ca";
}

.uniui-calendar-filled:before {
  content: "\e6c0";
}

.uniui-notification-filled:before {
  content: "\e6c1";
}

.uniui-wallet-filled:before {
  content: "\e6c2";
}

.uniui-medal-filled:before {
  content: "\e6c3";
}

.uniui-fire-filled:before {
  content: "\e6c5";
}

.uniui-refreshempty:before {
  content: "\e6bf";
}

.uniui-location-filled:before {
  content: "\e6af";
}

.uniui-person-filled:before {
  content: "\e69d";
}

.uniui-personadd-filled:before {
  content: "\e698";
}

.uniui-arrowthinleft:before {
  content: "\e6d2";
}

.uniui-arrowthinup:before {
  content: "\e6d3";
}

.uniui-arrowthindown:before {
  content: "\e6d4";
}

.uniui-back:before {
  content: "\e6b9";
}

.uniui-forward:before {
  content: "\e6ba";
}

.uniui-arrow-right:before {
  content: "\e6bb";
}

.uniui-arrow-left:before {
  content: "\e6bc";
}

.uniui-arrow-up:before {
  content: "\e6bd";
}

.uniui-arrow-down:before {
  content: "\e6be";
}

.uniui-arrowthinright:before {
  content: "\e6d1";
}

.uniui-down:before {
  content: "\e6b8";
}

.uniui-bottom:before {
  content: "\e6b8";
}

.uniui-arrowright:before {
  content: "\e6d5";
}

.uniui-right:before {
  content: "\e6b5";
}

.uniui-up:before {
  content: "\e6b6";
}

.uniui-top:before {
  content: "\e6b6";
}

.uniui-left:before {
  content: "\e6b7";
}

.uniui-arrowup:before {
  content: "\e6d6";
}

.uniui-eye:before {
  content: "\e651";
}

.uniui-eye-filled:before {
  content: "\e66a";
}

.uniui-eye-slash:before {
  content: "\e6b3";
}

.uniui-eye-slash-filled:before {
  content: "\e6b4";
}

.uniui-info-filled:before {
  content: "\e649";
}

.uniui-reload:before {
  content: "\e6b2";
}

.uniui-micoff-filled:before {
  content: "\e6b0";
}

.uniui-map-pin-ellipse:before {
  content: "\e6ac";
}

.uniui-map-pin:before {
  content: "\e6ad";
}

.uniui-location:before {
  content: "\e6ae";
}

.uniui-starhalf:before {
  content: "\e683";
}

.uniui-star:before {
  content: "\e688";
}

.uniui-star-filled:before {
  content: "\e68f";
}

.uniui-calendar:before {
  content: "\e6a0";
}

.uniui-fire:before {
  content: "\e6a1";
}

.uniui-medal:before {
  content: "\e6a2";
}

.uniui-font:before {
  content: "\e6a3";
}

.uniui-gift:before {
  content: "\e6a4";
}

.uniui-link:before {
  content: "\e6a5";
}

.uniui-notification:before {
  content: "\e6a6";
}

.uniui-staff:before {
  content: "\e6a7";
}

.uniui-vip:before {
  content: "\e6a8";
}

.uniui-folder-add:before {
  content: "\e6a9";
}

.uniui-tune:before {
  content: "\e6aa";
}

.uniui-auth:before {
  content: "\e6ab";
}

.uniui-person:before {
  content: "\e699";
}

.uniui-email-filled:before {
  content: "\e69a";
}

.uniui-phone-filled:before {
  content: "\e69b";
}

.uniui-phone:before {
  content: "\e69c";
}

.uniui-email:before {
  content: "\e69e";
}

.uniui-personadd:before {
  content: "\e69f";
}

.uniui-chatboxes-filled:before {
  content: "\e692";
}

.uniui-contact:before {
  content: "\e693";
}

.uniui-chatbubble-filled:before {
  content: "\e694";
}

.uniui-contact-filled:before {
  content: "\e695";
}

.uniui-chatboxes:before {
  content: "\e696";
}

.uniui-chatbubble:before {
  content: "\e697";
}

.uniui-upload-filled:before {
  content: "\e68e";
}

.uniui-upload:before {
  content: "\e690";
}

.uniui-weixin:before {
  content: "\e691";
}

.uniui-compose:before {
  content: "\e67f";
}

.uniui-qq:before {
  content: "\e680";
}

.uniui-download-filled:before {
  content: "\e681";
}

.uniui-pyq:before {
  content: "\e682";
}

.uniui-sound:before {
  content: "\e684";
}

.uniui-trash-filled:before {
  content: "\e685";
}

.uniui-sound-filled:before {
  content: "\e686";
}

.uniui-trash:before {
  content: "\e687";
}

.uniui-videocam-filled:before {
  content: "\e689";
}

.uniui-spinner-cycle:before {
  content: "\e68a";
}

.uniui-weibo:before {
  content: "\e68b";
}

.uniui-videocam:before {
  content: "\e68c";
}

.uniui-download:before {
  content: "\e68d";
}

.uniui-help:before {
  content: "\e679";
}

.uniui-navigate-filled:before {
  content: "\e67a";
}

.uniui-plusempty:before {
  content: "\e67b";
}

.uniui-smallcircle:before {
  content: "\e67c";
}

.uniui-minus-filled:before {
  content: "\e67d";
}

.uniui-micoff:before {
  content: "\e67e";
}

.uniui-closeempty:before {
  content: "\e66c";
}

.uniui-clear:before {
  content: "\e66d";
}

.uniui-navigate:before {
  content: "\e66e";
}

.uniui-minus:before {
  content: "\e66f";
}

.uniui-image:before {
  content: "\e670";
}

.uniui-mic:before {
  content: "\e671";
}

.uniui-paperplane:before {
  content: "\e672";
}

.uniui-close:before {
  content: "\e673";
}

.uniui-help-filled:before {
  content: "\e674";
}

.uniui-paperplane-filled:before {
  content: "\e675";
}

.uniui-plus:before {
  content: "\e676";
}

.uniui-mic-filled:before {
  content: "\e677";
}

.uniui-image-filled:before {
  content: "\e678";
}

.uniui-locked-filled:before {
  content: "\e668";
}

.uniui-info:before {
  content: "\e669";
}

.uniui-locked:before {
  content: "\e66b";
}

.uniui-camera-filled:before {
  content: "\e658";
}

.uniui-chat-filled:before {
  content: "\e659";
}

.uniui-camera:before {
  content: "\e65a";
}

.uniui-circle:before {
  content: "\e65b";
}

.uniui-checkmarkempty:before {
  content: "\e65c";
}

.uniui-chat:before {
  content: "\e65d";
}

.uniui-circle-filled:before {
  content: "\e65e";
}

.uniui-flag:before {
  content: "\e65f";
}

.uniui-flag-filled:before {
  content: "\e660";
}

.uniui-gear-filled:before {
  content: "\e661";
}

.uniui-home:before {
  content: "\e662";
}

.uniui-home-filled:before {
  content: "\e663";
}

.uniui-gear:before {
  content: "\e664";
}

.uniui-smallcircle-filled:before {
  content: "\e665";
}

.uniui-map-filled:before {
  content: "\e666";
}

.uniui-map:before {
  content: "\e667";
}

.uniui-refresh-filled:before {
  content: "\e656";
}

.uniui-refresh:before {
  content: "\e657";
}

.uniui-cloud-upload:before {
  content: "\e645";
}

.uniui-cloud-download-filled:before {
  content: "\e646";
}

.uniui-cloud-download:before {
  content: "\e647";
}

.uniui-cloud-upload-filled:before {
  content: "\e648";
}

.uniui-redo:before {
  content: "\e64a";
}

.uniui-images-filled:before {
  content: "\e64b";
}

.uniui-undo-filled:before {
  content: "\e64c";
}

.uniui-more:before {
  content: "\e64d";
}

.uniui-more-filled:before {
  content: "\e64e";
}

.uniui-undo:before {
  content: "\e64f";
}

.uniui-images:before {
  content: "\e650";
}

.uniui-paperclip:before {
  content: "\e652";
}

.uniui-settings:before {
  content: "\e653";
}

.uniui-search:before {
  content: "\e654";
}

.uniui-redo-filled:before {
  content: "\e655";
}

.uniui-list:before {
  content: "\e644";
}

.uniui-mail-open-filled:before {
  content: "\e63a";
}

.uniui-hand-down-filled:before {
  content: "\e63c";
}

.uniui-hand-down:before {
  content: "\e63d";
}

.uniui-hand-up-filled:before {
  content: "\e63e";
}

.uniui-hand-up:before {
  content: "\e63f";
}

.uniui-heart-filled:before {
  content: "\e641";
}

.uniui-mail-open:before {
  content: "\e643";
}

.uniui-heart:before {
  content: "\e639";
}

.uniui-loop:before {
  content: "\e633";
}

.uniui-pulldown:before {
  content: "\e632";
}

.uniui-scan:before {
  content: "\e62a";
}

.uniui-bars:before {
  content: "\e627";
}

.uniui-checkbox:before {
  content: "\e62b";
}

.uniui-checkbox-filled:before {
  content: "\e62c";
}

.uniui-shop:before {
  content: "\e62f";
}

.uniui-headphones:before {
  content: "\e630";
}

.uniui-cart:before {
  content: "\e631";
}
