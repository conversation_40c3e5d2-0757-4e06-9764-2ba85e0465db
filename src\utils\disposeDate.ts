export function timestampToTime(timestamp:any,format?:string) {
    const date = new Date(timestamp)
    const Y = date.getFullYear().toString()
    const M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1)
    const D = (date.getDate() < 10 ? '0'+(date.getDate()) : date.getDate())
    const h = (date.getHours() < 10 ? '0'+(date.getHours()) : date.getHours())
    const m = (date.getMinutes() < 10 ? '0'+(date.getMinutes()) : date.getMinutes())
    const s = (date.getSeconds() < 10 ? '0'+(date.getSeconds()) : date.getSeconds())
    if(format ){
        format=format.replace('YYYY',Y+'')
        format=format.replace('MM',M+'')
        format=format.replace('DD',D+'')
        format=format.replace('HH',h+'')
        format=format.replace('mm',m+'')
        format=format.replace('ss',s+'')
        // return new Promise((resolve,reject)=>{
        //     resolve(format)
        // })
        return  format
    }
    // return new Promise((resolve,reject)=>{
    //     resolve(Y+'-'+M+'-'+D+' '+h+':'+m+':'+s)
    // })
    return Y+'-'+M+'-'+D+' '+h+':'+m+':'+s

}
export function  disWeek(time:any) {
    const week=new Date(time).getDay()
        switch (week) {
            case 0:
                return '周一'
                break;
            case 1:
                return '周二'
                break;
            case 2:
                return '周三'
                break;
            case 3:
                return '周四'
                break;
            case 4:
                return '周五'
                break;
            case 5:
                return '周六'
                break;
            case 6:
                return '周七'
                break;
            default:
                return ''
        }
}
