// import {getDiseaseType} from '@/api/student'
// export async function diseaseType(id:number) {
//     const res=await getDiseaseType()
//     console.log(res)
// }
interface a {
    rows:any[]
}
const diseaseType={
    disType: {} as a,
    async set(){
        // let res=await getDiseaseType()
        // this.disType=res
    },
    get(key: number): null | any {
        let disName
        if(this.disType.rows){
            this.disType.rows.forEach((item:any)=>{
                if(item.diseaseDesc==key){
                    disName= item.diseaseName
                }
            })
        }
        return disName
    }
}
export default diseaseType
