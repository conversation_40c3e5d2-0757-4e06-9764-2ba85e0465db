<template>
  <view class="wrapper">
    <view class="content-wrapper">
      <view class="content">
        <slot></slot>
      </view>

    </view>
  </view>
</template>
<script setup lang="ts">

</script>
<style scoped lang="scss">
.wrapper {
  width: 100%;

  .content-wrapper {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    .content{
      width: 90%;
      display: flex;
      flex-wrap: wrap;

    }
  }
}
</style>
