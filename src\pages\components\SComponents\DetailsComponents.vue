<template>
  <view class="o-wrapper">
    <view class="splc-wrapper" v-if="props.source=='4'">
      <view class="splc-title list-first">
        审批流程:
      </view>
      <view class="splc-content">
        <uni-steps :options="props.detail.approve" active-color="#007AFF" :active="props.detail.stage"
                   direction="column"/>
      </view>
    </view>
    <view style="width: 100%;" v-for="(item,index) in props.detail.list" :key="index">
      <view v-if="props.detail.title && item.data"  :class="{'list-item-m-t':true,'list-first':(props.source!='4' &&index===0)}">
        {{ item.title }}
      </view>
      <template v-for="(ite,idx) in item.data" >
        <view class="list-item-o" v-for="(v,k) in item.detailTitleList" :key="k" >

          <template v-if="v.type==='text'">
            <view class="list-item-t">
              {{ v.title }}:
            </view>
            <view class="list-item-c">{{ ite[v.code] }}</view>
          </template>
          <template v-else-if="v.type==='imgArray'">
            <view class="list-item-img-w">
              <view style="width: 100%">
                <view class="list-item-img-t">{{ v.title }}:</view>
              </view>
              <view class="list-item-img" v-if="ite[v.code]" v-for="(it,id) in ite[v.code]" :key="index">
                <image style="width: 100%;height: 100%" :src="it.url" alt=""></image>
              </view>
            </view>
          </template>
          <template v-else-if="v.type==='select'">
            <view class="list-item-t">
              {{ v.title }}:
            </view>
<!--            <view class="list-item-c">{{ ite[v.code] }}</view>-->
            <view class="list-item-c">{{ v.options?v.options.find((ff:any)=>ff.value===ite[v.code]+'')?v.options.find((ff:any)=>ff.value===ite[v.code]+'').text:ite[v.code]:ite[v.code]}}</view>
          </template>


        </view>
      </template>
      <view style="height: 1rem" v-if="item.data"></view>
    </view>

    <view class="splc-wrapper" v-if="props.source!='4'">
      <view class="splc-title">
        审批流程:
      </view>
      <view class="splc-content">
        <uni-steps :options="props.detail.approve" active-color="#007AFF" :active="props.detail.stage"
                   direction="column"/>
      </view>
    </view>
    <view class="bottom-button-wrapper" v-if="props.source+''==='1'">
      <button @click="handleBackButton">
        拒绝
      </button>
      <button type="primary" @click="handleSubButton">
        通过
      </button>
    </view>
    <view class="bottom-button-wrapper" v-if="props.source+''==='2'">
      <button @click="handleBackClick">
        返回
      </button>
      <button type="primary" @click="handleEditApprove">
        修改审批状态
      </button>
    </view>
    <uni-popup :mask-click="false" ref="updatePopupRef" type="dialog">
      <view
          style="display: flex;flex-direction: column;width: 15rem;background-color: #ffffff;padding: 1rem;border-radius: 3px">
        <view style="font-size: 1rem;border-bottom: 1px silver solid;padding-bottom: 0.5rem;margin-bottom: 1rem">
          修改审批状态
        </view>
        <view style="display: flex;align-items: center;">
          <view style="min-width: 5rem;">审批状态:</view>
          <view style="min-width: 8rem;">
            <uni-data-select label="审批状态" placeholder="请选择审批状态" v-model="approveStatus"
                             :localdata="approveStatusList"/>
          </view>
        </view>
<!--        <view style="margin-top: 1rem">-->
<!--          <checkbox-group @change="handleChangeC">-->
<!--            <label>-->
<!--              <checkbox :value="'1'" :checked="isLongTime==='1'"/>-->
<!--              长期有效-->
<!--            </label>-->
<!--          </checkbox-group>-->
<!--        </view>-->
        <view style="display: flex;justify-content: flex-end;margin-top: 2rem">
          <button style="width: 5rem" @click="handleCloseEdit">
            取消
          </button>
          <button type="primary" style="width: 5rem" @click="handleEditSubButton">
            确定
          </button>
        </view>
      </view>


    </uni-popup>
    <uni-popup :mask-click="false" ref="inputDialog" type="dialog">
      <uni-popup-dialog ref="inputClose" mode="input" title="审批意见" value="审批意见"
                        placeholder="请输入原因" @confirm="dialogInputConfirm"></uni-popup-dialog>
    </uni-popup>
  </view>
</template>
<script setup lang="ts">
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import {ref, onMounted, getCurrentInstance} from "vue";
import uniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import uniPopupDialog from "@/pages/components/popup/uni-popup-dialog/uni-popup-dialog.vue";
import uniSteps from "@/pages/components/uni-steps/uni-steps-s.vue";
import UniDataSelect from "@/pages/components/uni-data-select/uni-data-select.vue";
import Headle from "@/pages/components/headle.vue";
import {approvalOrder} from "@/api/maininfo";
import {sexDict} from "@/api/generalDict";
import {onLoad} from "@dcloudio/uni-app";
import cache from "@/utils/cache";
import {USER_INFO} from "@/enums/cacheEnums";
import { getDicts } from "@/api/params";

const props = defineProps<{
  detail: any;
  source: string;
}>();
const selectedFist=ref(1)
const inputClose = ref()
const inputDialog = ref()
const userInfo=ref<any>()
const approvalResult=ref('0')
const handleSubButton = () => {
  approvalResult.value='2'
  inputDialog.value.open()
}
const handleBackButton = () => {
  approvalResult.value='3'
  inputDialog.value.open()
}
const updatePopupRef = ref()
const approveStatus = ref()
const isLongTime = ref('0')
onLoad(() => {

  userInfo.value=cache.get(USER_INFO)

})
onMounted(() => {
  console.log(props.detail.list[0].data[0], 'mainInfo');
  // 重新拼接地址数据
  const mainInfo = props.detail.list[0].data[0]
  mainInfo.visitorAddress = mainInfo.campus +'-'+ mainInfo.building +'-'+ mainInfo.roomNo
  // 审批流程
  console.log(props.detail.approve, 'props.detail.approve');
  // 翻译来访事由
      const purposeList = ref([])
      getDicts("reason_for_visit").then(res => {
        if (res) {
          purposeList.value = res.data.map((m: any) => {
            return {
              text: m.dictLabel,
              value: m.dictValue
            }
          })
          mainInfo.purpose = purposeList.value.find(m => m.value === mainInfo.purpose).text
        }
      })
props.detail.list[0].data[0] = mainInfo
})
const approveStatusList = ref([
  {value: "2", text: "通过"},
  {value: '3', text: "拒绝"},
])
const approvalOpinion=ref('')
const dialogInputConfirm =async (e: any) => {
  approvalOpinion.value=e
  await setApprovalOrder(e)
}
const setApprovalOrder = async (e:any) => {
  let jobNum1 =encodeURIComponent(userInfo.value.userName)
  await approvalOrder({
    id:props.detail.list[0].data[0].id,
    //jobNum:props.detail.list[0].data[0].jobNum,
    jobNum:jobNum1,
    approvalResult:approvalResult.value,
    approvalOpinion:e,
  }).then((res:any)=>{
    if(res){
      console.log(res)
      uni.showToast({
        title: '成功',
        icon: 'success'
      })
      uni.navigateBack();
    }
  })
}
const handleEditApprove = () => {
  updatePopupRef.value.open()
}
const handleBackClick = () => {
  uni.navigateBack()
}
const handleCloseEdit = () => {
  updatePopupRef.value.close()
}
const emit = defineEmits(['againGit'])
const handleEditSubButton = async () => {
  // console.log(isLongTime.value)
  await approvalOrder({
    id:props.detail.list[0].data[0].id,
    jobNum:props.detail.list[0].data[0].jobNum,
    approvalResult:approveStatus.value,
    approvalOpinion:""
  }).then((res:any)=>{
    if(res){
      console.log(res)
      uni.showToast({
        title: '成功',
        icon: 'success'
      })
      emit('againGit')
      updatePopupRef.value.close()

    }
  })
  // uni.showToast({
  //   title: '修改成功',
  //   icon: 'success'
  // })
  // updatePopupRef.value.close()
}
const handleChangeC = (e: any) => {
  if (e.detail.value[0]) {
    isLongTime.value = e.detail.value[0]
  } else {
    isLongTime.value = '0'
  }
}
</script>
<style scoped lang="scss">
.o-wrapper {
  width: 100%;
  border: 1px solid #c5c3c3;
  padding: 0rem 0 1rem 0;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  border-radius: 0.5rem;
  display: flex;
  justify-content: space-between;
  flex-direction: column;

  .list-item-m-t {
    width: 100%;
    background-image: linear-gradient(to right, #007AFF, #33CCFF);
    height: 1.8rem;
    padding-left: 0.5rem;
    display: flex;
    align-items: center;
    font-weight: 600;
    color: white;
    margin-bottom: 1rem;
  }
  .list-item-o {
    display: flex;
    justify-content: space-between;
    padding: 0 1rem;

    .list-item-t {
      min-width: 5rem;
    }

    .list-item-c {
      word-break: break-all;
      overflow-wrap: break-word
    }

    .list-item-img-w {
      display: flex;
      justify-content: space-between;
      flex-wrap: wrap;

      .list-item-img {
        width: 100px;
        height: 100px;
        margin: 1rem 0;
      }
    }
  }

  .list-item-o:not(:last-child) {
    margin-bottom: 0.3rem;
  }

  .splc-wrapper {
    width: 100%;
    padding: 0 0rem;

    .splc-title {
      width: 100%;
      background-image: linear-gradient(to right, #007AFF, #33CCFF);
      height: 1.8rem;
      padding-left: 0.5rem;
      display: flex;
      align-items: center;
      font-weight: 600;
      color: white;
      margin-bottom: 1rem;
    }
    .splc-content {
      padding-left: 5rem;
    }
  }

  .bottom-button-wrapper {
    margin-top: 1rem;
    width: 100%;
    display: flex;

    button {
      width: 9rem;
    }
  }

  .popup-content {

  }
}
.list-first{
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
}
</style>
