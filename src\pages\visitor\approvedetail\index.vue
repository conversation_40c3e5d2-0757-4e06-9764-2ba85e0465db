<template>
  <view style="width: 100%">
    <headle></headle>

    <wrapper-component>
      <DetailsComponents v-if="showDetailList" :source="source" :detail="detailData"
      ></DetailsComponents>
    </wrapper-component>
  </view>
</template>
<script setup lang="ts">
import {ref, onMounted, getCurrentInstance} from "vue";
import {onLoad} from "@dcloudio/uni-app";
import DetailsComponents from "@/pages/components/SComponents/DetailsComponents.vue";
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import tp from '@/static/logo.png'
import Headle from "@/pages/components/headle.vue";
import {getMainInfoByIdAuth} from "@/api/authToken";

const showDetailList = ref(false);
const detailData = ref<any>();
const listData = ref<any>([
  {
    title: "访客详情",
    id: 1,
    list: [{
      title: '来访人员信息',
      data: [{
        name: '张毅',
        phone: '12345678901',
        sex: '女',
        time: '2022-01-01 00:00:00',
        reason: '开会',
        area: '会议室',
        visitStatus: '未到访',
        applyStatus: '审核通过',
        information: [
          {
            url: tp,
            name: '图片1'
          },
        ],
      }],
      detailTitleList:[
        {
          title: '姓名',
          code: 'name',
          type: 'text'
        },
        {
          title: '性别',
          code: 'sex',
          type: 'text'
        },
        {
          title: '手机号',
          code: 'phone',
          type: 'text'
        },
        {
          title: '来访时间',
          code: 'time',
          type: 'text'
        },
        {
          title: '来访事由',
          code: 'reason',
          type: 'text'
        },
        {
          title: '来访区域',
          code: 'area',
          type: 'text'
        },
        {
          title: '上传资料',
          code: 'information',
          type: 'imgArray'
        },
      ]
    },
      {
        title: '同行人员信息',
        data:[{
          tx: '赵倩',
          sjh:'13365251452'
        },{
          tx: '张鑫',
          sjh:'13365251452'
        },{
          tx: '周瑟',
          sjh:'13365251452'
        },{
          tx: '林锦',
          sjh:'13365251452,'
        },
        ],

        detailTitleList: [
          {
            title: '姓名',
            code: 'tx',
            type: 'text'
          },{
            title: '手机号',
            code: 'sjh',
            type: 'text'
          },
        ]
      }
    ],
    approve: [
      {
        result: '已通过',
        name: '王老师',
        desc: '2025-08-08',
        opinion: '通过',
        color:"red"
      },
      {
        result: '未通过',
        name: '曹老师',
        desc: '2025-08-08',
        opinion: '不通过',
      },
      {
        result: '待审核',
        name: '张老师',
        desc: '2025-08-08',
        opinion: '',
      }
    ],
    stage: 1
  }
])
onMounted(() => {

})
const source = ref<any>(null);
onLoad(async (query: any) => {
  if(query.id){
    await getMainInfoByIdAuth(query.id).then((res:any)=>{
      console.log(res)
    })
  }

  // detailData.value = listData.value.find((item: any) => (item.id + '') === query.id)
  source.value = query.source
  // showDetailList.value = true
})
</script>
<style scoped lang="scss">

</style>
