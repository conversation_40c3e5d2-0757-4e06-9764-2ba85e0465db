import request from '@/utils/request/index'
import type {MainInfoParams} from "@/api/maininfo";

export interface RegisterWeixin{
    visitorPhoneNumber:string
    vwuReservedText1:string
    visitorIdNumber:string
    visitorName:string
}
export function getVCode(phone:string) {
    return request.get({
        url:'/visitor/weixinuser/generatevcode?phone='+phone
    })
}
export function loginByWeixin(data:RegisterWeixin) {
    return request.post({
        url:'/visitor/weixinuser/loginbyweixin',
        data:data
    })
}
