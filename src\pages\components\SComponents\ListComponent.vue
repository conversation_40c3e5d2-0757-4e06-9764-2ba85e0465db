<template>
  <view class="wrapper">
    <view class="list-wrapper">
      <view class="list-item" v-for="(item,index) in props.list" :key="index">
        <template v-for="(v,k) in props.titleList" :key="k">
          <view class="list-item-o"  v-if="v.type==='text'">
            <view class="list-item-t">
              {{v.title}}:
            </view>
            <view class="list-item-c">{{item[v.code]}}</view>
          </view>
          <view class="list-item-o"  v-else-if="v.type==='select'">
            <view class="list-item-t">
              {{v.title}}:
            </view>
<!--            <view class="list-item-c">{{item[v.code]}}</view>-->
            <view class="list-item-c">
              {{ v.options?v.options.find((ff:any)=>ff.value===item[v.code]+'')?v.options.find((ff:any)=>ff.value===item[v.code]+'').text:item[v.code]:item[v.code]}}
            </view>
          </view>
          <template v-else-if="v.type==='imgArray'">
            <view class="list-item-img-w">
              <view style="width: 100%">
                <view class="list-item-img-t">{{ v.title }}:</view>
              </view>
              <view class="list-item-img" v-if="item[v.code]"  v-for="(it,id) in item[v.code]" :key="index"><image style="width: 100%;height: 100%" :src="it.url" alt=""></image></view>
            </view>
          </template>
        </template>

        <view class="bottom-button">
          <view>
            <button  v-if="props.source+''==='3'" type="primary" style="width: 6rem;margin-right: 1rem" size="mini" @click="handleGJClick(item)">
              人员轨迹
            </button>
            <button v-if="props.source+''!=='3'" type="primary" style="width: 6rem" size="mini" @click="handleDetailsClick(item)">
              查看详情
            </button>
          </view>

        </view>
      </view>
    </view>
  </view>
</template>
<script setup lang="ts">
import {ref,defineOptions,defineProps,defineEmits,defineModel} from 'vue'
const props = defineProps<{
  list: Array<any>
  titleList: Array<string>
  source:string
}>()
const emit = defineEmits<{
  selectItem:any
}>()
const handleDetailsClick=(e:any)=>{
  emit('selectItem',e)
}
const handleGJClick=(e:any)=>{
  uni.navigateTo({
    url:'/pages/visitor/personneltrajectory/index'
  })
}
</script>
<style scoped lang="scss">
.wrapper {
  width: 100%;
  .list-wrapper {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    flex-direction: column;
    justify-content: center;
    width: 100%;
    .list-item {
      width:90%;
      border: 1px solid #c5c3c3;
      margin-bottom: 1rem;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      border-radius: 0.5rem;
      background-color: rgb(248, 248, 248);
      display: flex;
      flex-direction: column;
      align-items: stretch;
      box-sizing: border-box;
      padding: 1rem;
      .list-item-o{
        display: flex;
        justify-content: space-between;

        .list-item-t{
        }
        .list-item-c{}
      }
      .list-item-o:not(:last-child) {
        margin-bottom: 0.3rem;
      }
      .list-item-img-w{
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-wrap: wrap ;
        .list-item-img{
          width: 100px;
          height: 100px;
          margin: 1rem 0;
        }
      }
      .bottom-button{
        width: 100%;
        display: flex;
        flex-direction: column-reverse;
        flex-wrap: wrap;
        align-content: flex-end;
        align-self: flex-end;
        border-top: 1px solid #c5c3c3;
        padding-top: 0.5rem;
        margin-top: 0.5rem;
      }
    }
  }
}
</style>
