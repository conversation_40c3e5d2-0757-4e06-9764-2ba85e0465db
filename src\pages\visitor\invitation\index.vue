<template>
  <view class="wrapper" style="background-color: rgb(248, 248, 248); ">
    <headle></headle>
    <wrapper-component >
      <view style="padding: 0 0.5rem">
        <view style="margin-top: 1rem;font-weight: 600">
          访客信息
        </view>
        <view>
          <form-component ref="inputFormRef" :input-data="inputData" :rules="inputDataRule" @submit="handleMainInfoSub($event)"></form-component>
        </view>
        <template v-if="true">
          <view style="display: flex">
            <button  v-if="txInputData.length==0"  style="width: 8rem"  type="primary" @click="addTxInputData">
              添加同行人员
            </button>
            <button  v-if="txCarInputData.length==0"  style="width: 8rem"  type="primary" @click="addTxCarInputData">
              添加同行车辆
            </button>
          </view>
        </template>
        <template v-if="txInputData.length>0">
          <view style="margin-top: 1rem;font-weight: 600">
            同行人员
          </view>
          <view v-for="(item,index) in txInputData" :key="index">
            <form-component v-if="showTxPerson" :input-data="item" :form-data="formData.vPersonInfoS[index]" @submit="handleTxPersonSub($event,item,index)"></form-component>
            <view style="display: flex">
              <button v-if="index===txInputData.length-1" style="width: 6rem" size="mini" type="primary" @click="addTxInputData">
                添加
              </button>
              <button  style="width: 6rem" size="mini" @click="removeTxIn(index)">
                删除
              </button>
            </view>
          </view>
        </template>
        <template v-if="txCarInputData.length>0">
          <view style="margin-top: 1rem;font-weight: 600">
            同行车辆
          </view>
          <view v-for="(item,index) in txCarInputData" :key="index">
            <form-component v-if="showCar" :input-data="item" :form-data="formData.vCarInfoS[index]" @submit="handleTxCarSub($event,item,index)"></form-component>
            <view style="display: flex">
              <button v-if="index===txCarInputData.length-1" style="width: 6rem" size="mini" type="primary" @click="addTxCarInputData">
                添加
              </button>
              <button  style="width: 6rem" size="mini" @click="removeTxCarIn(index)">
                删除
              </button>
            </view>
          </view>
        </template>
        <view style="margin-top: 1rem;font-weight: 600">
          拜访信息
        </view>
        <view>
          <form-component :input-data="bfInputData"></form-component>
        </view>
        <button type="primary" style="margin-bottom: 0.5rem" @click="handleSubButton">
          提交
        </button>
      </view>

    </wrapper-component>
    <uni-popup :mask-click="false" ref="xzDialog" type="dialog" title="入校须知">
      <view style="background-color: white;padding:1rem;width: 15rem">
        <view style="font-weight:600;display: flex;align-items: center;justify-content: center;margin-bottom: 0.5rem">
          入校须知
        </view>
        <view style="">
          <view style="text-indent: 2em;">禁止携带易燃易爆、管制刀具等危险物品入校。</view>
          <view style="text-indent: 2em;">校内禁止吸烟、酗酒、赌博或大声喧哗。</view>
        </view>
        <view style="display: flex;justify-content: flex-end;margin-top: 2rem" >
          <!--          <button  style="width: 5rem" @click="handleJjDialog">-->
          <!--            取消-->
          <!--          </button>-->
          <button type="primary"  style="width: 5rem" @click="handleCloseDialog">
            确定
          </button>
          <!--          <view style="height: 2rem;width: 50%; display: flex;align-items: center;justify-content: center;font-size: 18px">取消</view>-->
          <!--          <view style="height: 2rem;width: 50%; display: flex;align-items: center;justify-content: center;font-size: 18px;background-color: #007AFF;color: white">确定</view>-->
        </view>
      </view>
    </uni-popup>

  </view>
</template>
<script setup lang="ts">
import formComponent from '@/pages/components/SComponents/FormComponent.vue'
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import {nextTick, onMounted, ref} from "vue";
import uniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import Headle from "@/pages/components/headle.vue";
import {onLoad} from "@dcloudio/uni-app";
import {getVisitaddress} from "@/api/address";
import {addMainInfo, type MainInfoParams} from "@/api/maininfo";
import cache from "@/utils/cache";
import {USER_INFO} from "@/enums/cacheEnums";
import {sexDict} from "@/api/generalDict";
import {getDicts} from "@/api/params";
const reasonList=ref([])
getDicts("reason_for_visit").then(res=>{
  if(res){
    reasonList.value = res.data.map((m:any)=>{
      return{
        text:m.dictLabel,
        value: m.dictValue
      }
    })
    inputData.value.forEach(item=>{
      if(item.code==='purpose'){
        item.options=reasonList.value
      }
    })
  }
})
const inputFormRef=ref()
const formData=ref<MainInfoParams>({
  actualBeginTime: "",
  actualEndTime: "",
  approvalResult: "",
  beginTime: "",
  building: "",
  buildingId: "",
  campus: "",
  campusId: "",
  carCount: 0,
  dtdTime: "",
  endTime: "",
  filePath1: "",
  filePath2: "",
  filePath3: "",
  id: 0,
  imgPath1: "",
  imgPath2: "",
  imgPath3: "",
  isDtd: "",
  isOtherCars: "",
  isOtherVisitors: "",
  jobNum: "",
  orgcode: "",
  purpose: "",
  roomNo: "",
  teacherName: "",
  vCarInfoS: [],
  vPersonInfoS: [],
  visitId: "",
  visitorCarInfor: "",
  visitorCount: 0,
  visitorGender: "",
  visitorIdNumber: "",
  visitorName: "",
  visitorPhoneNumber: "",
  visitorWorkplace: ""
})
const inputData = ref([
  {
    label: '姓名',
    type: 'text',
    placeholder: '请输入姓名',
    code: 'visitorName',
  },
  {
    label: '证件号码',
    type: 'text',
    placeholder: '请输入证件号码',
    code: 'visitorIdNumber'
  },
  {
    label: '手机号',
    type: 'text',
    placeholder: '请输入手机号',
    code: 'visitorPhoneNumber'
  },
  {
    label: '车牌号',
    type: 'text',
    placeholder: '请输入车牌号',
    code: 'visitorCarInfor'
  },
  {
    label: '工作单位',
    type: 'text',
    placeholder: '请输入工作单位',
    code: 'visitorWorkplace',
  },
  {
    label: '来访缘由',
    type: 'select',
    placeholder: '请选择来访缘由',
    code: 'purpose',
    options: reasonList.value
  },
  {
    label: '性别',
    type: 'select',
    placeholder: '请选择性别',
    code: 'visitorGender',
    options: sexDict
  },
  {
    label: "开始时间",
    type: 'date',
    placeholder: '请选择时间',
    code: 'beginTime',
    dateType:'datetime',
    end:'endTime'
  },
  {
    label: "结束时间",
    type: 'date',
    placeholder: '请选择时间',
    code: 'endTime',
    dateType:'datetime',
    start:'beginTime'
  },
  {
    label: "人员照片",
    type: 'imgList',
    placeholder: '请上传图片',
    code: 'pic',
    limit:1
  },
  {
    label: "相关证明资料",
    type: 'imgList',
    placeholder: '请上传图片',
    code: 'information',
    limit:3
  },
])

const inputDataRule=ref({
  visitorName: {
    rules: [{
      required: true,
      errorMessage: '请输入访客姓名',
    },
      // {
      //   minLength: 3,
      //   maxLength: 5,
      //   errorMessage: '姓名长度在 {minLength} 到 {maxLength} 个字符',
      // }
    ]
  },
  visitorIdNumber: {
    rules: [{
      required: true,
      errorMessage: '请输入证件号码',
    },
      {
        minLength: 18,
        maxLength: 18,
        errorMessage: '请输入正确的证件号码',
      }
    ]
  },
  visitorPhoneNumber: {
    rules: [{
      required: true,
      errorMessage: '请输入手机号',
    },
      {
        minLength: 11,
        maxLength: 11,
        errorMessage: '请输入正确的手机号码',
      }
    ]
  },
  visitorWorkplace: {
    rules: [{
      required: true,
      errorMessage: '工作单位',
    }
    ]
  },
  purpose: {
    rules: [{
      required: true,
      errorMessage: '请选择来访缘由',
    }
    ]
  },
  visitorGender: {
    rules: [{
      required: true,
      errorMessage: '请选择性别',
    }
    ]
  },
  beginTime: {
    rules: [{
      required: true,
      errorMessage: '请选择开始时间',
    }
    ]
  },
  endTime: {
    rules: [{
      required: true,
      errorMessage: '请选择结束时间',
    }
    ]
  },
})

const bfInputData=ref([
  {
    label: '拜访校区',
    type: 'text',
    placeholder: '请输入拜访校区',
    code: 'campus',
    value:''
  },
  {
    label: '拜访楼宇',
    type: 'text',
    placeholder: '请输入拜访楼宇',
    code: 'building',
    value:''
  },
  {
    label: '拜访房间',
    type: 'text',
    placeholder: '请输入拜访房间',
    code: 'roomNo',
    value:''
  },
  {
    label: '拜访单位',
    type: 'text',
    placeholder: '请输入拜访单位',
    code: 'svaReservedText2',
    value:''
  },
])
const areaData=ref()
const userInfo=ref()
onLoad(async (query:any)=>{
  if(query.areaid){
    await handleGetAreaData(query.areaid)
    userInfo.value=cache.get(USER_INFO)
  }else {
    uni.showToast({
      title:'获取区域id失败',
      icon:'error'
    })
    uni.navigateBack()
  }
})
const handleGetAreaData=async (id:number)=>{
  await getVisitaddress(id).then(res=>{
    if(res && res.data){
      bfInputData.value.forEach(f=>{
        f.value=res.data[f.code]
      })
      areaData.value=res.data
    }
  })
}
onMounted(async ()=>{
  // xzDialog.value.open();
})
const xzDialog=ref()
const handleCloseDialog=()=>{
  xzDialog.value.close();
}
const handleJjDialog=()=>{
  window.close()
}
const handleSubButton=async ()=>{
  if(await inputFormRef.value.handleSubmitRules()){
    let subData=JSON.parse(JSON.stringify(formData.value))
    if(subData.pic && subData.pic.length>0){
      subData.pic.forEach((f:any,index:number)=>{
        subData['imgPath'+(index+1)]=f
      })
    }
    if(subData.information && subData.information.length>0){
      subData.information.forEach((f:any,index:number)=>{
        subData['filePath'+(index+1)]=f
      })
    }
    delete subData.pic
    delete subData.information
    if(subData.vPersonInfoS && subData.vPersonInfoS.length>0){
      subData.vPersonInfoS.forEach((ff:any,index:number)=>{
        if(ff.pic && ff.pic.length>0){
          ff.pic.forEach((f:any,index:number)=>{
            ff['imgPath'+(index+1)]=f
          })
        }
        if(ff.information && ff.information.length>0){
          ff.information.forEach((f:any,index:number)=>{
            ff['filePath'+(index+1)]=f
          })
        }
        delete ff.pic
        delete ff.information
      })
    }

    //基础信息
    subData.jobNum=userInfo.value.userName
    subData.teacherName=userInfo.value.nickName
    subData.campusId=areaData.value.campusId
    subData.campus=areaData.value.campus
    subData.buildingId=areaData.value.buildingId
    subData.building=areaData.value.building
    subData.roomNo=areaData.value.roomNo
    await addMainInfo(subData).then(res=>{
      if(res){
        uni.showToast({
          title:'提交成功',
          icon:'success'
        })
        uni.navigateBack()
      }
    })
  }


  // uni.showToast({
  //   title:'提交成功',
  //   icon:'success'
  // })
  // uni.navigateBack()
}
const txInputData = ref<any[]>([])
const addTxInputData=()=>{
  txInputData.value.push([
    {
      label: '姓名',
      type: 'text',
      placeholder: '请输入姓名',
      code: 'visitorName',
    },
    {
      label: '证件号码',
      type: 'text',
      placeholder: '请输入证件号码',
      code: 'visitorIdNumber'
    },
    {
      label: '手机号',
      type: 'text',
      placeholder: '请输入手机号',
      code: 'visitorPhoneNumber'
    },
    {
      label: '性别',
      type: 'select',
      placeholder: '请选择性别',
      code: 'visitorGender',
      options: sexDict
    },
    {
      label: '开始时间',
      type: 'date',
      placeholder: '请选择开始时间',
      code: 'beginTime'
    },
    {
      label: '结束时间',
      type: 'date',
      placeholder: '请选择结束时间',
      code: 'endTime'
    },
    {
      label: "人员照片",
      type: 'imgList',
      placeholder: '请上传图片',
      code: 'pic',
      limit:1
    },
    {
      label: "相关证明资料",
      type: 'imgList',
      placeholder: '请上传图片',
      code: 'information',
      limit:3
    },
  ])
  formData.value.vPersonInfoS.push({
    beginTime: "",
    endTime: "",
    pic:[],
    id: 0,
    information:[],
    isDtd: "",
    orgcode: "",
    type: "",
    visitId: "",
    visitorGender: "",
    visitorIdNumber: "",
    visitorName: "",
    visitorPhoneNumber: "",
    visitorWorkplace: ""
  })
}
const showTxPerson=ref(true)
const removeTxIn=(index:number)=>{
  showTxPerson.value=false
  txInputData.value.splice(index,1)
  formData.value.vPersonInfoS.splice(index,1)
  showTxPerson.value=true
  nextTick()
}
const handleTxPersonSub=(d:any,item:any,index:number)=>{
  formData.value.vPersonInfoS[index]=d
}


const txCarInputData = ref<any[]>([])
const addTxCarInputData=()=>{
  txCarInputData.value.push([
    {
      label: '车牌号',
      type: 'text',
      placeholder: '请输入车牌号',
      code: 'visitorCarInfor',
    }])
  formData.value.vCarInfoS.push({
    id:0,
    orgcode:"",
    visitId:"",
    visitorCarInfor:"",
    type:"",
    beginTime:'',
    endTime:''
  })
}
const showCar=ref(true)
const removeTxCarIn=(index:number)=>{
  showCar.value=false
  txCarInputData.value.splice(index,1)
  formData.value.vCarInfoS.splice(index,1)
  showCar.value=true
  nextTick()
}
const handleTxCarSub=(d:any,item:any,index:number)=>{
  formData.value.vCarInfoS[index]=d
}

const handleMainInfoSub=(d:any)=>{
  formData.value.visitorName=d.visitorName
  formData.value.visitorIdNumber=d.visitorIdNumber
  formData.value.visitorPhoneNumber=d.visitorPhoneNumber
  formData.value.visitorCarInfor=d.visitorCarInfor
  formData.value.visitorWorkplace=d.visitorWorkplace
  formData.value.purpose=d.purpose
  formData.value.visitorGender=d.visitorGender
  formData.value.beginTime=d.beginTime
  formData.value.endTime=d.endTime
  formData.value.pic=d.pic
  formData.value.information=d.information
}
</script>
<style scoped lang="scss">

</style>
