<template>
  <view class="wrapper">
    <headle></headle>
    <form-component :input-data="inputData" ref="inputFormRef" :rules="inputDataRule" @submit="handleSub"></form-component>
    <uni-popup :mask-click="false" ref="xzDialog" type="dialog" title="入校须知">
      <view style="background-color: white;padding:1rem;width: 15rem">
        <view style="display: flex;align-items: center;justify-content: center;margin-bottom: 0.5rem">
          用户协议
        </view>
        <view style="">
          <view style="text-indent: 2em;">
            1.需提供 真实、准确、完整 的注册信息（如手机号、邮箱等），并及时更新。
          </view>
         <view style="text-indent: 2em;">2.您需妥善保管账户及密码，因泄露或非授权使用导致的损失，本服务不承担责任。</view>
         <view style="text-indent: 2em;">3.禁止转让、出租或共享账户，否则我们有权暂停或注销账户。</view>
        </view>
        <view style="display: flex;justify-content: flex-end;margin-top: 2rem">
          <button style="width: 5rem" @click="handleJjDialog">
            拒绝
          </button>
          <button type="primary" style="width: 5rem" @click="handleCloseDialog">
            同意
          </button>
        </view>
      </view>
    </uni-popup>
    <button type="primary" @click="handleOpenDial">提交</button>
  </view>
</template>
<script setup lang="ts">
import formComponent from '@/pages/components/SComponents/FormComponent.vue'
import Headle from "@/pages/components/headle.vue";
import uniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import {onMounted, ref} from "vue";
import {loginByWeixin} from "@/api/wuser";
import {useUserStore} from "@/stores/user";
import cache from "@/utils/cache";
const formData = ref<any>()
const userStore =useUserStore()
const handleOpenDial=async ()=>{
  if(await inputFormRef.value.handleSubmitRules()){
    // console.log(formData.value)
    // uni.showToast({
    //   title: '提交成功',
    //   icon:'success'
    // })
    await loginByWeixin(formData.value).then(async res=>{
      if(res){
        await userStore.login(res.msg)
        cache.set('wphone',formData.value.visitorPhoneNumber)
        uni.redirectTo({
          url: '/pages/visitor/wxlist/index'
        })
      }

    })

  }

}
const inputFormRef=ref()
const handleSub=(e:any)=>{
  formData.value=e
}
const inputData = [
  {
    label: '姓名',
    type: 'text',
    placeholder: '请输入姓名',
    code: 'visitorName'
  },
  {
    label: '证件类型',
    type: 'select',
    placeholder: '请选择证件类型',
    code: 'sex',
    options: [
      {
        text: '身份证',
        value: '1'
      },
    ]
  },
  {
    label: '证件号码',
    type: 'text',
    placeholder: '请输入证件号码',
    code: 'visitorIdNumber'
  },
  {
    label: '手机号',
    type: 'text',
    placeholder: '请输入手机号',
    code: 'visitorPhoneNumber'
  },
  {
    label: '验证码',
    type: 'yzm',
    placeholder: '请输入验证码',
    code: 'vwuReservedText1',
    phoneCode:'visitorPhoneNumber'
  },

]
const inputDataRule=ref({
  visitorName: {
    rules: [{
      required: true,
      errorMessage: '请输入姓名',
    },
    ]
  },
  visitorIdNumber: {
    rules: [{
      required: true,
      errorMessage: '请输入证件号码',
    },
      {
        minLength: 18,
        maxLength: 18,
        errorMessage: '请输入正确的证件号码',
      }
    ]
  },
  visitorPhoneNumber: {
    rules: [{
      required: true,
      errorMessage: '请输入手机号',
    },
      {
        minLength: 11,
        maxLength: 11,
        errorMessage: '请输入正确的手机号码',
      }
    ]
  },
  vwuReservedText1: {
    rules: [{
      required: true,
      errorMessage: '请输入验证码',
    },
    ]
  },
})
const xzDialog=ref()
const handleCloseDialog=()=>{
  xzDialog.value.close();
}
const handleJjDialog=()=>{
  window.close()
}
onMounted(()=>{
  xzDialog.value.open();
})
</script>

<style scoped lang="scss">

</style>
