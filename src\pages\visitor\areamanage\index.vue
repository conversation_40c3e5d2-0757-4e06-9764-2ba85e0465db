<template>
  <view>
    <headle></headle>
    <view class="address-container">
      <view class="list-item" v-for="(item, index) in areaList" :key="index">
        <view class="list-item-o">
          <view class="list-item-t">
            拜访校区:
          </view>
          <view class="list-item-c">{{ item.campus }}</view>
        </view>
        <view class="list-item-o">
          <view class="list-item-t">
            拜访楼宇:
          </view>
          <view class="list-item-c">{{ item.building }}</view>
        </view>
        <view class="list-item-o">
          <view class="list-item-t">
            拜访房间:
          </view>
          <view class="list-item-c">{{ item.roomNo }}</view>
        </view>
        <view class="list-item-o">
          <view class="list-item-t">
            拜访单位:
          </view>
          <view class="list-item-c">{{ item.svaReservedText2 }}</view>
        </view>
        <view style="width: 100%;display: flex;justify-content: flex-start">
          <label class="radio">
            <radio value="r2" :checked="item.isDefault+''==='1'" @click="handleClickMr(item)"/>
            默认区域信息</label>
        </view>
        <view class="bottom-button">
          <view>
            <button style="width: 5rem;margin-right: 1rem" size="mini" @click="editAddress(item)">
              编辑
            </button>
            <button type="warn" style="width: 5rem" size="mini" @click="deleteAddress(item)">
              删除
            </button>
          </view>

        </view>
      </view>
      <button :disabled="showButtonC" class="add-button" type="primary" @click="addNewAddress">新增区域信息</button>
    </view>
  </view>

</template>
<script setup lang="ts">
import {ref, onMounted} from 'vue';
import Headle from "@/pages/components/headle.vue";
import {onLoad, onReachBottom, onShow} from "@dcloudio/uni-app";
import {type AddressParams, delVisitaddress, getAddressList, setDefaultAddress} from "@/api/address";
import type {AreaParams} from "@/api/area";
import {add} from "lodash-es";

const showButtonC = ref(false)
// 地址列表
const areaList = ref<AddressParams[]>([]);
onLoad((query: any) => {})
const queryParams=ref({
  pageNum:1,
  pageSize:10
})
// 获取地址列表
const areaTotal=ref()
const getAreaList = async () => {
  // 这里应该是调用接口获取数据的逻辑
  getAddressList(queryParams.value).then((res:any)=>{
    if(res){
      if(queryParams.value.pageNum===1){
        areaList.value=res.rows
      }else {
        areaList.value=[...areaList.value,...res.rows]
      }
      areaTotal.value=res.total
    }
  })
};
onReachBottom(()=>{
  if(areaTotal.value>(queryParams.value.pageNum*queryParams.value.pageSize)){
    queryParams.value.pageNum+=1
    getAreaList()
  }
})
// 编辑地址
const editAddress = (area: any) => {
  // 跳转到编辑页面并传递地址信息
  uni.navigateTo({
    url: `/pages/visitor/areamanage/addArea?areaId=${area.id + ''}`
  });
};

// 删除地址
const deleteAddress = (address: any) => {
  console.log(address.id)
  uni.showModal({
    title: '提示',
    content: `确定要删除地址吗？`,
    success: (res) => {
      if (res.confirm) {
        // 执行删除操作
        delVisitaddress(address.id).then(res=>{
          if(res){
            uni.showToast({
              title:'删除成功',
              icon:'success'
            })
            getAreaList()
          }
        })
      }
    }
  });
};

// 新增地址
const addNewAddress = () => {
  showButtonC.value = true
  setTimeout(() => {
    showButtonC.value = false
    uni.navigateTo({
      url: '/pages/visitor/areamanage/addArea'
    });
  }, 100)

};
const handleClickMr = async (area: any) => {
  await setDefaultAddress(area.id).then(res=>{
    if(res){
      uni.showToast({
        title:'修改成功',
        icon:'success'
      })
      getAreaList()
    }
  })
  // if (area.isDefault) {
  //
  // } else {
  //   areaList.value = areaList.value.map(item => {
  //     return {
  //       ...item,
  //       isDefault: item.id === area.id
  //     }
  //   })
  //
  // }
}
// 页面加载时获取地址列表
onMounted(() => {

});
onShow(()=>{
  getAreaList();
})
</script>
<style scoped lang="scss">
.address-container {
  display: flex;
  flex-direction: column;
  margin-top: 1rem;
  justify-content: center;
  align-items: center;
}

.list-item {
  width: 90%;
  border: 1px solid #c5c3c3;
  margin-bottom: 1rem;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
  align-items: stretch;
  box-sizing: border-box;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: rgb(248, 248, 248);

  .list-item-o {
    display: flex;
    justify-content: space-between;

    .list-item-t {
    }

    .list-item-c {
    }
  }

  .list-item-o:not(:last-child) {
    margin-bottom: 0.3rem;
  }

  .bottom-button {
    width: 100%;
    display: flex;
    flex-direction: column-reverse;
    flex-wrap: wrap;
    align-content: flex-end;
    align-self: flex-end;
    border-top: 1px solid #c5c3c3;
    padding-top: 0.5rem;
    margin-top: 0.5rem;
  }
}

.add-button {
  position: sticky;
  bottom: 12px;
  z-index: 10;
  //background-color: #07c160;
}
</style>
