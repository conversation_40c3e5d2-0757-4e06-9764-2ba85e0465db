<template>
  <div class="wra-h">
    <div class="headleWrapper">
      <!--      <image style="width: 100%;height: 3.5rem" :src="imgSrc"></image>-->
      <div
        style="color: white;width: 100%;display: flex;align-items: center;justify-content: center;font-size: 25px;font-weight: 600;text-align: justify;letter-spacing: 3px">
        {{ sysTitle }}
      </div>
    </div>
  </div>

</template>
<script setup lang="ts">
import { onMounted, ref } from 'vue'
import { getConfigKey, getDicts } from "@/api/params";

const sysTitle = ref("")
onMounted(() => {
  getConfigKey("visitSystemTitle").then(res => {
    if (res.msg) {
      sysTitle.value = res.msg
    }
  })
})



</script>
<style scoped lang="scss">
.wra-h {
  height: 4.5rem;

  .headleWrapper {
    width: 100%;
    position: fixed;
    height: 4rem;
    background-image: linear-gradient(to top, rgb(3, 89, 170), rgb(36, 159, 235), rgb(66, 180, 250));
    display: flex;
    align-items: center;
    z-index: 10;

    image {
      width: 100%;
    }
  }
}
</style>
