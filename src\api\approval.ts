import request from '@/utils/request/index'

// export interface ApprovalParams{
//     id:number
//     jobNum:string
//     approvalResult:string
//     approvalOpinion:string
// }

export function getApprovalWithMainInfoList(data:any) {
    console.log(data)
    return request.get({
        url:'visitor/approvalresult/list/queryApprovalWithMain',
        data
    })
}

export function getApprovalWithPersonList(data:any) {
    console.log(data)
    return request.get({
        url:'visitor/approvalresult/list/queryApprovalWithPerson',
        data
    })
}