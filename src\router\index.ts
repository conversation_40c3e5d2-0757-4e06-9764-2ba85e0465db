import { BACK_URL,USER_ROLE } from '@/enums/cacheEnums'
import { useUserStore } from '@/stores/user'
import { getToken } from '@/utils/auth'
import cache from '@/utils/cache'
import { routes } from './routes'

const whiteList = ['register', 'login', 'forget_pwd','index']
const list = ['navigateTo', 'redirectTo', 'reLaunch', 'switchTab']
list.forEach((item,index) => {
    uni.addInterceptor(item, {
        invoke(e) {
            // 获取要跳转的页面路径（url去掉"?"和"?"后的参数）
            const url = e.url.split('?')[0]
            const url2=url.replace('../','/pages/')
            const currentRoute = routes.find((item) => {
                return url2 === item.path

            })
            console.log(444444444444)
            console.log(currentRoute)
            //需要登录并且没有token
            if (currentRoute?.auth && !getToken()) {
                if(currentRoute.path.includes('wx')){
                    uni.navigateTo({
                        url: '/pages/visitor/register/index'
                    })
                }else{
                    uni.navigateTo({
                        url: '/pages/login/index'
                    })
                }

                return false
            }

            return e
        },
        fail(err) {
            // 失败回调拦截
            console.log(err)
        }
    })
})

export function setupRouter() {
    // #ifdef H5
    // const app = getApp()
    // app.$router.afterEach((to: any, from: any) => {
    //     const index = whiteList.findIndex((item) => from.path.includes(item) || from.path === '/')
    //     const userStore = useUserStore()
    //     if (index == -1 && !userStore.isLogin) {
    //         //保存登录前的路径
    //         cache.set(BACK_URL, from.fullPath)
    //     }
    // })


    // #endif
}
