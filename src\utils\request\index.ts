import HttpRequest from './http'
import { merge } from 'lodash-es'
import type { HttpRequestOptions, RequestHooks } from './type'
import { getToken } from '../auth'
import { RequestCodeEnum } from '@/enums/requestEnums'
import { useUserStore } from '@/stores/user'

const requestHooks: RequestHooks = {
    requestInterceptorsHook(options:any, config : any) {
        const { urlPrefix, baseUrl, withToken,isfile } = config
        options.header = options.header ?? {}
        if (urlPrefix) {
            options.url = `${urlPrefix}${options.url}`
        }
        if (baseUrl) {
            options.url = `${baseUrl}${options.url}`
        }
        if(isfile){
            options.responseType='blob'
        }
        if(options.data){
            Object.keys(options.data).forEach(key=>{
                if(!options.data[key]){
                    delete options.data[key]
                }
            })
        }

        const token = getToken()
        if (withToken && token) {
            // options.header.token = token
            // console.log(options)
            options.header['Authorization'] = 'Bearer ' + token
        }
        // console.log(options)
        return options
    },
    responseInterceptorsHook(response, config) {
        const { isTransformResponse, isReturnDefaultResponse, isAuth } = config

        //返回默认响应，当需要获取响应头及其他数据时可使用
        if (isReturnDefaultResponse) {
            return response
        }
        // 是否需要对数据进行处理
        if (!isTransformResponse) {
            return response.data
        }
        console.log(response.data, 'response.data')
        const { logout } = useUserStore()
        const { code, data, msg } = response.data as any
        switch (code) {
            case RequestCodeEnum.SUCCESS:
                return response.data
            case RequestCodeEnum.PARAMS_TYPE_ERROR:
            case RequestCodeEnum.PARAMS_VALID_ERROR:
            case RequestCodeEnum.REQUEST_METHOD_ERROR:
            case RequestCodeEnum.ASSERT_ARGUMENT_ERROR:
            case RequestCodeEnum.ASSERT_MYBATIS_ERROR:
            case RequestCodeEnum.LOGIN_ACCOUNT_ERROR:
            case RequestCodeEnum.LOGIN_DISABLE_ERROR:
            case RequestCodeEnum.NO_PERMISSTION:
            case RequestCodeEnum.FAILED:
            case RequestCodeEnum.SYSTEM_ERROR:
                uni.showToast({
                    title:msg,
                    icon:"error"

                })
                // return Promise.reject()
                return null

            case RequestCodeEnum.TOKEN_INVALID:
            case RequestCodeEnum.TOKEN_EMPTY:

                return response
            case RequestCodeEnum.SYSTEM_NOPOWER:
                uni.showToast({
                    title:'没权限啊,重新登录吧',
                    icon:"error"

                })
                logout()
                if(location.href.includes("wx")){
                    if (!getToken()) {
                        uni.navigateTo({
                            url: '/pages/visitor/register/index'
                        })
                    }
                }else {
                    if (!getToken()) {
                        uni.navigateTo({
                            url: '/pages/login/index'
                        })
                    }
                }
                return ''
            case 666:
                uni.showToast({
                    title:msg,
                    icon:"error"

                })
                // return Promise.reject()
                return ''
            default:
                return ''
        }
    }
}

const defaultOptions: HttpRequestOptions = {
    requestOptions: {
        timeout: 60 * 1000
    },
    baseUrl: `${import.meta.env.VITE_APP_BASE_URL || ''}/`,
    //是否返回默认的响应
    isReturnDefaultResponse: false,
    // 需要对返回数据进行处理
    isTransformResponse: true,
    // 接口拼接地址
    urlPrefix: '',
    // 忽略重复请求
    ignoreCancel: false,
    // 是否携带token
    withToken: true,
    isAuth: false,
    requestHooks: requestHooks,
    isfile:false
}

function createRequest(opt?: HttpRequestOptions) {
    return new HttpRequest(
        // 深度合并
        merge(defaultOptions, opt || {})
    )
}
const request = createRequest()
export default request
