import request from "@/utils/request";
import type {MainInfoParams} from "@/api/maininfo";

export function getVisitaddressAuth(id:number) {
    return request.get({
        url: '/visitor/visitaddress/auth/' + id
    },{
        isAuth:false
    })
}
export function getBaseInfoYYMAuth(authCode:string) {
    return request.get({
        url: '/visitor/basicinfo/getbaseinfobyyym?authCode='+authCode
    },{
        isAuth:false
    })
}
export function addMainInfoAuth(params:MainInfoParams) {
    return request.post({
        url:'/visitor/maininfo/addmainauth',
        data:params
    },{
        isAuth:false
    })
}

export function getMainIdByYYM(authCode:string){
    return request.get({
        url:'/visitor/maininfo/getmainidbyyym?yym='+authCode
    },{
        isAuth:false
    })
}
export function getMainInfoByIdAuth(id:number){
    return request.get({
        url:'/visitor/maininfo/getorderinfoauth?id='+id
    },{
        isAuth:false
    })
}
