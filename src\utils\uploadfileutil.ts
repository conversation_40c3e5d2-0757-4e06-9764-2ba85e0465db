import { getToken } from './auth'
export interface uploadParams {
    fileUrl:string,
    uploadUrl:string,
    name:string,
    f:any,
    biz:string
}
export async function uploadImg(body:uploadParams) {
    console.log(body.f)
    let iURl
    uni.showLoading({
        title:'上传中'
    })
    return new Promise((resolve,reject)=>{
         uni.uploadFile({
            url:body.uploadUrl,
            filePath:body.fileUrl,
            name:body.name,
            file:body.f[0],
            formData:{
                'file':body.f[0],
                'files':body.f,
                'biz':body.biz
            },
            header:{
                'Authorization' : 'Bearer ' + getToken()
            },
            timeout:200000,
            success:(res)=>{
                uni.hideLoading()
                if(JSON.parse(res.data).success){
                    uni.showToast({
                        title:'上传成功'
                    })
                    // console.log(JSON.parse(res.data).data)
                    resolve(JSON.parse(res.data).data.fileUrl)
                }
                // console.log(JSON.parse(res.data).data.fileUrl)
            },
            fail:(res)=>{
                uni.hideLoading()
                // console.log(res)
            },
            complete:(res)=>{
                // console.log(res)
            }
        })
    })

}
