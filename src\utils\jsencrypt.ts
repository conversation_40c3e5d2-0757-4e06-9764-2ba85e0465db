import JSEncrypt from 'jsencrypt'

// 密钥对生成 http://web.chacuo.net/netrsakeypair

// const publicKey = '-----BEGIN PUBLIC KEY-----MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAMcxjP6qPt7IajkgB8EQMmPRlZ0OCT23w2zzvywFDdpLde5Uk5kpP6o1WtFldOVamcrZSmJmQ4o/z+2Ift+aEJUCAwEAAQ==-----<PERSON><PERSON> PUBLIC KEY-----'
//
// const privateKey ='-----BEGIN PRIVATE KEY-----MIIBVQIBADANBgkqhkiG9w0BAQEFAASCAT8wggE7AgEAAkEAxzGM/qo+3shqOSAHwRAyY9GVnQ4JPbfDbPO/LAUN2kt17lSTmSk/qjVa0WV05VqZytlKYmZDij/P7Yh+35oQlQIDAQABAkEAieXZ0CuX3q8sKmPsOlG2MLhVNScUaq+fEPwlapoNHwNJIdkwS1AW18/UPdalDplT07el38Gv/pz8Ihy3YtS5gQIhAPLY3IgsEMBOQXyt5NY7tAma92aYYxGIbRW94ncyauJxAiEA0ftr3gj8AIQ2sne3oQlslv9Nr7Nx6e2eFB8OCQiDWmUCIQDOHVHQIdZ14fWjJNS9IB9Gp8ijw2MSVoB/m5LDKH+fgQIgMwsWBoxSjeGV1j5J0giTcAUxokNRrpbHdYazB7kdDQ0CIDG9uObA26w2Qwka3d038djjFjrvUhmzVcpprzYwzOLt-----END PRIVATE KEY-----'


let publicKey ="-----BEGIN PUBLIC KEY-----MFwwDQYJKoZIhvcNAQEBBQADSwAwSAJBAKoR8mX0rGKLqzcWmOzbfj64K8ZIgOdHnzkXSOVOZbFu/TJhZ7rFAN+eaGkl3C4buccQd/EjEsj9ir7ijT7h96MCAwEAAQ==-----END PUBLIC KEY-----"

let privateKey = '-----BEGIN PRIVATE KEY-----MIIBVAIBADANBgkqhkiG9w0BAQEFAASCAT4wggE6AgEAAkEAqmqFRp9BSI2SLVkmgv75m2UGn0Ic2WrEhVGTSEojn1EbBFb1gNGn/6nzkgUDAcwfmaIYY/5M6Ema0MYT/Js/wQIDAQABAkAK1Iw1ycc3Wi/xC9pjttqcIpaCJox40M0+f8mxbWzIIh/Iz92A4Xyt5Ya5+qPbMo2QLWpmfBiXdVjblobTaLXVAiEA4geMi6AT7bzU1l357U9/Srk7PLRkhOyqUZCCXFh5YlcCIQDBAzRb8OgqSZS47GoefvO4/Pvvi41MluKxf7va7P8PpwIhAI50xY7mqEjJH7ev6So5VXfqALzE0Sm546qekprTJEXnAiBHxlxLMGv1hcVTez7/NjCGAzXg36Fc8OM+JIUEJYu4EwIgfJ09i3MmoCTIbBi14UIp+Y/CepUK964xS9v6LnkqgeI=-----END PRIVATE KEY-----'

// 加密
export function encrypt(txt:string) {
    const encryptor = new JSEncrypt()
    encryptor.setPublicKey(publicKey) // 设置公钥
    return encryptor.encrypt(txt) // 对数据进行加密
}

// 解密
export function decrypt(txt:string) {
    const encryptor = new JSEncrypt()
    encryptor.setPrivateKey(privateKey) // 设置私钥
    return encryptor.decrypt(txt) // 对数据进行解密
}
