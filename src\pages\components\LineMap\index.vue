<template>
  <view  :class="{wrapperMap:true}">
        <view v-show="mOT==='m'" ref="mapContainer" class="amap-container" id="mapwrapper"></view>
<!--    <headle-com v-show="mOT==='t'"/>-->
    <view :class="{twrapper:mOT==='t'}">

      <view v-show="mOT==='t'" class="content-wrapper">
        <view class="i-1-w" v-for="(item,index) in logisticsInfoList">
          <view class="item-title">
            <view class="item-title-i">
              {{index+1}}
            </view>
            <view class="item-title-c">
              {{item.happenTime}}
            </view>
          </view>
<!--          <view class="item-c-w">-->
<!--            <view class="item-c-t">-->
<!--              设备IP:-->
<!--            </view>-->
<!--            <view class="item-c-c">-->
<!--              {{item.deviceIp}}-->
<!--            </view>-->
<!--          </view>-->
          <view class="item-c-w">
            <view class="item-c-t">
              通行地点:
            </view>
            <view class="item-c-c">
              {{addres[index]}}
            </view>
          </view>
          <view class="item-c-w">
            <view class="item-c-t">
              通行时间:
            </view>
            <view class="item-c-c">
              {{addresT[index]}}
            </view>
          </view>
<!--          <view class="item-c-w">-->
<!--            <view class="item-c-t">-->
<!--              经度:-->
<!--            </view>-->
<!--            <view class="item-c-c">-->
<!--              {{item.longitude}}-->
<!--            </view>-->
<!--          </view>-->
<!--          <view class="item-c-w">-->
<!--            <view class="item-c-t">-->
<!--              纬度:-->
<!--            </view>-->
<!--            <view class="item-c-c">-->
<!--              {{item.latitude}}-->
<!--            </view>-->
<!--          </view>-->
          <view class="item-c-w">
            <view class="item-c-t">
              人员编号:
            </view>
            <view class="item-c-c">
              {{item.userCode}}0215
            </view>
          </view>
          <view class="item-c-w">
            <view class="item-c-t">
              人员名称:
            </view>
            <view class="item-c-c">
              {{item.name}}张毅
            </view>
          </view>
          <view  >
            <view class="item-c-t">
              现场照片:
            </view>
            <view class="item-c-c" style="margin-top: 0.5rem;display: flex" >
              <image :src="zp" style="width: 5rem;height: 5rem"></image>
            </view>
          </view>
        </view>

      </view>
    </view>
  </view>

</template>

<script setup>
import {ref,reactive, onMounted ,getCurrentInstance} from 'vue';
import AMapLoader from '@amap/amap-jsapi-loader';
import zp from "@/static/logo2.png"
const proxy=getCurrentInstance()
// 开发环境：配置JsCode
window._AMapSecurityConfig = {
  securityJsCode:'85fa3f974b4f48610853178b25b3a149'
};
const queryParams=reactive({
  userCode:null,
  happenTime:null
})
const addres=ref([
    '操场',
    '教学楼',
    '会议室',
    '逸夫楼',
    '图书馆',
    '操场'
])
const addresT=ref([
  '2025-07-17 10:50:25',
  '2025-07-17 11:22:25',
  '2025-07-17 12:30:35',
  '2025-07-17 15:20:22',
  '2025-07-17 16:20:22',
  '2025-07-17 17:56:25'
])
queryParams.happenTime=new Date()

const props=defineProps(['personnelData'])
const selectPI=defineModel('selectPI')
const selectD=defineModel('selectD')
const mOT=defineModel('mOT')
mOT.value='m'
const logisticsInfoList = ref([
  {latitude: '37.426761', longitude: '121.506288'},
  {latitude: '37.427178', longitude: '121.510643'},
  {latitude: '37.426446', longitude: '121.508943'},
  {latitude: '37.425226', longitude: '121.511293'}
]);
const contentList=ref([])
const lineList=ref([])
logisticsInfoList.value.map(res=> {
  lineList.value.push([res.longitude, res.latitude]);
})
const startIsEnd=ref(false)
const dataList=ref([] )
const getLocalList=async ()=>{
  queryParams.happenTime=selectD.value
  queryParams.userCode=selectPI.value.jobNum
  // loading.value=true
  // await getPersonnelTrajectoryList(queryParams).then(e=>{
  //   if(e.code===200){
  //     loading.value=false
  //     logisticsInfoList.value=e.data
  //     lineList.value=[]
  //     contentList.value=[]
  //     if(logisticsInfoList.value.length>1 &&logisticsInfoList.value[0].deviceIp===logisticsInfoList.value[logisticsInfoList.value.length-1].deviceIp){
  //       startIsEnd.value=true
  //     }else {
  //       startIsEnd.value=false
  //     }
  //     logisticsInfoList.value.map(res=>{
  //       lineList.value.push([res.longitude,res.latitude ]);
  //       // if(contentList.value.filter(m=>{m.longitude===res.longitude && m.latitude===res.latitude}).length===0){
  //       let cIndex=contentList.value.findIndex(m=>{return m.deviceIp===res.deviceIp})
  //       console.log(res.deviceIp)
  //       console.log(contentList.value)
  //       if(cIndex===-1){
  //         contentList.value.push({
  //           ...res,
  //           dateList:[res.happenTime]
  //         })
  //       }else{
  //         contentList.value[cIndex].dateList.push(res.happenTime)
  //       }
  //
  //       // if(contentList.value.filter(m=>{m.longitude===res.longitude && m.latitude===res.latitude}).length===0){
  //       //   contentList.value.push({
  //       //     ...res,
  //       //
  //       //   })
  //       // }
  //     })
  //     console.log(contentList)
  //   }
  // })



}



/** 初始化地图函数 */
onMounted(async () => {
  initMap()
});
const showPoly=async ()=>{

  await getLocalList()
  initMap()
}



import qi from '@/static/icon/qi.png'
import zhong from '@/static/icon/zhong.png'

const initMap = () => {
  AMapLoader.load({
    "key": "d7f14bdb6cef3d7dc42946b4f86e2831",
    "version": "2.0",
    "plugins": ['AMap.PolylineEditor'],
  })
      .then((AMap) => {

        // 初始化地图
        const map = new AMap.Map('mapwrapper', {
          // 地图风格
          mapStyle: 'amap://styles/normal',
          // 缩放比例
          zoom: 17,
          center:['121.507498','37.426209']
        });

        // 加载插件 - AMap.Polyline 折线解决方案
        AMap.plugin('AMap.Polyline', function () {
          // 插件加载完成后，初始化 Driving 实例
          if (logisticsInfoList?.value && logisticsInfoList.value.length >= 2) {

            const driving = new AMap.Polyline({

              map,
              showTraffic: false,
              hideMarkers:true,
            });
            console.log(lineList.value)
            var polyline1 = new AMap.Polyline({
              path: lineList.value,
              strokeColor: "rgb(64, 158, 255)",
              strokeWeight: 6,
              strokeOpacity: 0.9,
              zIndex: 50,
              bubble: true,
              showDir:true

            })
            map.add([ polyline1])
            map.setFitView()
            var polyEditor;
            // var polyEditor = new AMap.PolyEditor(map, polygon)
            // polyEditor = new AMap.PolygonEditor(map)
            // polyEditor = new AMap.PolylineEditor(map, polyline1);
            // polyEditor.setTarget(polyline1);
            // polyEditor.open();
            let infoWinList=[]
            for(let i=0;i<contentList.value.length;i++){
              var startIcon = new AMap.Icon({
                // 图标尺寸
                size: new AMap.Size(25, 34),
                // 图标的取图地址
                image: qi,
                // 图标所用图片大小
                imageSize: new AMap.Size(25, 34),
                // 图标取图偏移量
                imageOffset: new AMap.Pixel(-9, -3)
              });
              var viaIcon = new AMap.Icon({
                size: new AMap.Size(25, 34),
                image: '//a.amap.com/jsapi_demos/static/demo-center/icons/dir-via-marker.png',
                imageSize: new AMap.Size(25, 34),
                imageOffset: new AMap.Pixel(-0, -0)
              });
              var endIcon = new AMap.Icon({
                size: new AMap.Size(25, 34),
                image: zhong,
                imageSize: new AMap.Size(25,34),
                imageOffset: new AMap.Pixel(0, 0)
              });

              let iconSelect;
              if(i===0){
                iconSelect=startIcon
              }else if(i===contentList.value.length-1){
                iconSelect=startIsEnd.value?viaIcon:endIcon
              }else {
                iconSelect=viaIcon
              }
              var viaMarker = new AMap.Marker({
                position: new AMap.LngLat(contentList.value[i]?.longitude,contentList.value[i]?.latitude),
                // 将一张图片的地址设置为 icon
                icon: iconSelect,
                // 设置了 icon 以后，设置 icon 的偏移量，以 icon 的 [center bottom] 为原点
                offset: new AMap.Pixel(-13, -30)
              });
              var info = [];
              // info.push("<div class='input-card content-window-card'><div><img style=\"float:left;width:67px;height:16px;\" src=\" https://webapi.amap.com/images/autonavi.png \"/></div> ");
              info.push("<div style=\"padding:7px 0px 0px 0px;\"><div style='text-align: center;width: 100%;font-size: 18px;font-weight: 600;margin-bottom: 10px'>监控信息</div>");
              info.push("<div class='input-item'><div class='item-t'>监控名称:</div> "+contentList.value[i].deviceName+"</div>");
              info.push("<div class='input-item'><div class='item-t'>监控IP : </div> "+contentList.value[i].deviceIp+"</div>");
              contentList.value[i].dateList.map((ll,ii)=>{
                if(ii==0){
                  info.push("<div class='input-item'><div class='item-t'>经过时间:</div> "+ll+"</div>");
                }else {
                  info.push("<div class='input-item'><div class='item-t'></div>"+ll+"</div>");
                }

              })
              info.push("<div class='input-item'><div class='item-t'>经纬度:</div>"+contentList.value[i].longitude+"----"+contentList.value[i].latitude+"</p></div></div>");
              var infoWindow = new AMap.InfoWindow({
                anchor: 'bottom-center',
                content: info.join(""),
                offset: new AMap.Pixel(0, -34)
              });
              infoWinList[i]=infoWindow;
              viaMarker.on('mouseover',()=>{

                infoWinList[i].open(map, [contentList.value[i]?.longitude,contentList.value[i]?.latitude]);
                // infoWindow.on('mouseout',()=>{
                //   infoWindow.close()
                // })

              })
              viaMarker.on('mouseout',()=>{
                infoWinList[i].close()
              })
              map.add([viaMarker]);

            }
          }else if(logisticsInfoList.value.length ===1){
            var startIcon = new AMap.Icon({
              // 图标尺寸
              size: new AMap.Size(25, 34),
              // 图标的取图地址
              image: qi,
              // 图标所用图片大小
              imageSize: new AMap.Size(25, 34),
              // 图标取图偏移量
              imageOffset: new AMap.Pixel(-9, -3)
            });
            var viaMarker = new AMap.Marker({
              position: new AMap.LngLat(contentList.value[0]?.longitude,contentList.value[0]?.latitude),
              // 将一张图片的地址设置为 icon
              icon: startIcon,
              // 设置了 icon 以后，设置 icon 的偏移量，以 icon 的 [center bottom] 为原点
              offset: new AMap.Pixel(-13, -30)
            });
            let infoWinList=[]
            var info = [];
            // info.push("<div class='input-card content-window-card'><div><img style=\"float:left;width:67px;height:16px;\" src=\" https://webapi.amap.com/images/autonavi.png \"/></div> ");
            info.push("<div style=\"padding:7px 0px 0px 0px;\"><div style='text-align: center;width: 100%;font-size: 18px;font-weight: 600;margin-bottom: 10px'>监控信息</div>");
            info.push("<div class='input-item'><div class='item-t'>监控名称:</div> "+contentList.value[0].deviceName+"</div>");
            info.push("<div class='input-item'><div class='item-t'>监控IP : </div> "+contentList.value[0].deviceIp+"</div>");
            contentList.value[0].dateList.map((ll,ii)=>{
              if(ii==0){
                info.push("<div class='input-item'><div class='item-t'>经过时间:</div> "+ll+"</div>");
              }else {
                info.push("<div class='input-item'><div class='item-t'></div>"+ll+"</div>");
              }

            })
            info.push("<div class='input-item'><div class='item-t'>经纬度:</div>"+contentList.value[0].longitude+"----"+contentList.value[0].latitude+"</p></div></div>");
            var infoWindow = new AMap.InfoWindow({
              anchor: 'bottom-center',
              content: info.join(""),
              offset: new AMap.Pixel(0, -34)
            });
            infoWinList[0]=infoWindow;
            viaMarker.on('mouseover',()=>{

              infoWinList[0].open(map, [contentList.value[0]?.longitude,contentList.value[0]?.latitude]);
              // infoWindow.on('mouseout',()=>{
              //   infoWindow.close()
              // })

            })
            viaMarker.on('mouseout',()=>{
              infoWinList[0].close()
            })
            map.add([viaMarker]);
          }
        });


      })
      .catch(e => {
        console.log(e);
      });
};
//切换
const editableTabsValue=ref('1')
const handleChangeTab=(e)=>{
  if(e==='1'){
    initMap()
  }
}
const editableTabs = ref([
  {
    title: '地图轨迹',
    name: '1',
  },
  {
    title: '人员轨迹数据',
    name: '2',
  },
])
const loading=ref(false)


defineExpose({
  getLocalList,
  showPoly,
  initMap

})
</script>
<style >
.wrapperMap{
  background-color: #F2F4F7;
  min-height: calc(100vh - 60px);

}
.twrapper{
  padding-top: 10px;
  padding-right: 10px;
  padding-left: 10px;
  padding-bottom: 60px;
}
.amap-container{
  width: 100%;
  height: calc(100vh - 60px);
}
.tablewrapper {
  width: 100%;
  height: 950px;
}
.input-item{
  display: flex;
  width: 300px;
  justify-content: start;
}
.item-t{
  width: 70px;
  text-align: left;
  padding-right: 10px;
}
.content-wrapper{
  display: flex;
  flex-direction: column;
}
.i-1-w{
  background-color: white;
  border-top: 3px #F2F4F7 dashed;
  padding: 10px;
}
.i-1-w:first-child{
  border: none;
}
.item-title{
  display: flex;
  align-items: center;
  padding-bottom: 1rem;
}
.item-title-i{
  background-color: #2278F9;
  width: 1.8rem;
  height: 1.8rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 0.5rem;
  font-size: 1.2rem;
  color: white;

}
.item-title-c{
  font-size: 1.1rem;
}
.item-c-w{
  display: flex;
  align-items: center;
}
.item-c-t{
  width: 30%;
  display: flex;
  align-items: center;
  justify-content: start;
}
.item-c-c{

}
</style>











<!--<template>-->
<!--  <div style="width: 100%;height: 500px">-->
<!--    <el-amap vid="amapDemo" :center="center" :zoom="zoom" class="amap-container" @init="init">-->
<!--&lt;!&ndash;    <el-amap vid="amapDemo" :center="center" :zoom="zoom" class="amap-container" >&ndash;&gt;-->
<!--&lt;!&ndash;      <el-amap-driving ref="tessss" :start="start" :end="end" @complete="onRouteComplete" />&ndash;&gt;-->
<!--&lt;!&ndash;      <el-amap-loca>&ndash;&gt;-->
<!--&lt;!&ndash;        <el-amap-loca-line&ndash;&gt;-->
<!--&lt;!&ndash;            :source-data="test"&ndash;&gt;-->
<!--&lt;!&ndash;        />&ndash;&gt;-->
<!--&lt;!&ndash;      </el-amap-loca>&ndash;&gt;-->
<!--      <el-amap-control-control-bar />-->
<!--    </el-amap>-->
<!--  </div>-->
<!--</template>-->
<!--<script setup>-->
<!--import {ElAmap} from "@vuemap/vue-amap";-->
<!--import {ElAmapLoca, ElAmapLocaLine} from "@vuemap/vue-amap-loca";-->
<!--import AMapLoader from '@amap/amap-jsapi-loader';-->
<!--const center=ref([116.397428, 39.90923]) // 北京的经纬度-->
<!--const zoom=ref(10)-->
<!--const start=([116.379028, 39.865042]) // 起点-->
<!--const end=ref([116.427281, 39.903719] )  // 终点-->
<!--let map = null;-->
<!--const init = (e) => {-->
<!--  map = e;-->
<!--  const driving = new AMap.Driving({-->
<!--    map: e,-->
<!--    panel: 'panel'-->
<!--  });-->

<!--  // 设置起点和终点-->
<!--  const startLngLat = [116.379028, 39.865042];-->
<!--  const endLngLat = [116.427281, 39.903719];-->
<!--  driving.search(new AMap.LngLat(...startLngLat), new AMap.LngLat(...endLngLat), (status, result) => {-->
<!--    if (status === 'complete') {-->
<!--      console.log('绘制完成', result);-->
<!--    } else {-->

<!--      console.error('获取驾车数据失败：', result);-->
<!--    }-->
<!--  });-->
<!--}-->
<!--const onRouteComplete=(data) =>{-->
<!--  console.log('绘制完成',data);-->
<!--}-->

<!--</script>-->
<!--<style scoped lang="scss">-->

<!--</style>-->
