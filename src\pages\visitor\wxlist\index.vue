<template>
  <view class="wrapper">
    <headle></headle>
    <view style="background-color: white;margin:1rem 0 0.5rem 5%;width: 15rem">
      <view style="display: flex;">
<!--        <view :class="{'tab-item':true,'selected-tab-item':active===3}" @click="handleClickTab(3)">已预约</view>-->
        <view :class="{'tab-item':true,'selected-tab-item':active===1}" @click="handleClickTab(1)">进行中</view>
        <view :class="{'tab-item':true,'selected-tab-item':active===2}" @click="handleClickTab(2)">已结束</view>
      </view>
    </view>
    <list-component :source="'2'" :title-list="titleListData" :list="listData" @select-item="handleDetailClick"></list-component>
    <view v-if="false" class="add-item" @click="addForm"><uni-icons type="plusempty" size="30"></uni-icons></view>
    <view v-if="true" style="width: 100%;display: flex;align-items: center;justify-content: center;position: fixed;bottom: 1rem">
      <view  @click="addForm" style="border-radius: 10px;width: 8rem;height: 2rem;background-color: #007AFF;color: white;display: flex;align-items: center;justify-content: center">新增预约</view>
    </view>

  </view>
</template>
<script setup lang="ts">
import {ref} from "vue"
import Headle from "@/pages/components/headle.vue";
import ListComponent from "@/pages/components/SComponents/ListComponent.vue";
import uniIcons from "@/pages/components/uni-icons/uni-icons.vue";
import {onLoad, onReachBottom} from "@dcloudio/uni-app";
import {
  getMainInfoListByJXZ,
  getMainInfoListByTeacher,
  getMainInfoListByTeacherId,
  getMainInfoListByYJS
} from "@/api/maininfo";
import cache from "@/utils/cache";
import {approveResultList, sexDict} from "@/api/generalDict";
const active = ref(1)
const orderTotal=ref()
const queryParams=ref({
  pageNum:1,
  pageSize:10,
  createBy:cache.get('wphone')
})
const getOrderList=async ()=>{
  if(active.value===1){
    await getMainInfoListByJXZ(queryParams.value).then((res:any)=>{
      if(res){
        if(queryParams.value.pageNum===1){
          listData.value=[]
          listData.value=res.rows
          orderTotal.value=res.total
        }else {
          listData.value=[...listData.value,...res.rows]
        }

      }
    })
  }else if(active.value===2){
    await getMainInfoListByYJS(queryParams.value).then((res:any)=>{
      if(res){
        if(queryParams.value.pageNum===1){
          listData.value=[]
          listData.value=res.rows
          orderTotal.value=res.total
        }else {
          listData.value=[...listData.value,...res.rows]
        }
      }
    })
  }

}
onReachBottom(()=>{
  if(orderTotal.value>(queryParams.value.pageNum*queryParams.value.pageSize)){
    queryParams.value.pageNum+=1
    getOrderList()
  }
})
onLoad(async (query:any)=>{
  getOrderList()
})
const titleListData=ref([
  {
    title:'审核状态',
    type:'select',
    code:'approvalResult',
    options:approveResultList
  },
  {
    title:'拜访人',
    type:'text',
    code:'teacherName'
  },
  {
    title:'拜访时间',
    type:'text',
    code:'beginTime'
  },
  {
    title:'拜访区域',
    type:'text',
    code:'campus'
  }
] as any[])
const listData=ref([] as any[])
const handleDetailClick=(e:any)=>{
  uni.navigateTo({
    url:'/pages/visitor/generalPage/details?id='+e.id+'&source=4',
    success(result) {
      // result.eventChannel.emit('detail',e)
      // result.eventChannel.emit('detailTitle',titleDataListDetails.value)
    },
  })
}
const handleClickTab=(e:number)=>{
  active.value=e
  queryParams.value.pageNum=1
  getOrderList()

}
const addForm = () => {
  uni.navigateTo({
    url: '/pages/visitor/wxform/index'
  });
};
</script>
<style scoped lang="scss">
.wrapper{
  .tab-item{
    margin-right: 1rem;
    padding-bottom: 0.3rem;
  }
  .selected-tab-item{
    border-bottom: 2px solid #007AFF;
    color: #007AFF;
  }
  .add-item {
    position: fixed;
    right: 1.5rem;
    bottom: 1.5rem;
    width: 2.5rem;
    height: 2.5rem;
    border: #494a4c 1px solid;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
