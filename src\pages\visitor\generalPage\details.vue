<template>
  <view style="width: 100%">
    <headle></headle>
    <wrapper-component>
      <DetailsComponents v-if="showDetailList" :source="source" :detail="detailData" @again-git="getMainInfoDetails">
      </DetailsComponents>
    </wrapper-component>
  </view>
</template>
<script setup lang="ts">
import { ref, onMounted, getCurrentInstance } from "vue";
import { onLoad } from "@dcloudio/uni-app";
import DetailsComponents from "@/pages/components/SComponents/DetailsComponents.vue";
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import tp from '@/static/logo.png'
import Headle from "@/pages/components/headle.vue";
import { getMainInfoDetailsByIdAuth } from "@/api/maininfo";
import { sexDict } from "@/api/generalDict";

const showDetailList = ref<any>(false);
const detailData = ref<any>();
const listData = ref<any>([
  {
    title: "访客详情",
    id: 1,
    list: [
      {
        title: '来访信息',
        data: [],
        detailTitleList: [
          {
            title: '拜访人',
            placeholder: '请输入拜访人姓名',
            code: 'teacherName',
            type: 'text',
          },
          {
            title: '拜访地址',
            placeholder: '请输入拜访地址',
            code: 'visitorAddress',
            type: 'text',
          }
          // {
          //   title: '拜访楼宇',
          //   type: 'text',
          //   placeholder: '请输入拜访楼宇',
          //   code: 'building',
          // },
          // {
          //   title: '拜访房间',
          //   type: 'text',
          //   placeholder: '请输入拜访房间',
          //   code: 'roomNo',
          // },
          // {
          //   title: '拜访单位',
          //   type: 'text',
          //   placeholder: '请输入拜访单位',
          //   code: 'visitorWorkplace',
          // },
        ]
      },
      {
        title: '来访人员信息',
        data: [],
        detailTitleList: [
          {
            title: '姓名',
            code: 'visitorName',
            type: 'text'
          },
          {
            title: '性别',
            code: 'visitorGender',
            type: 'select',
            options: sexDict
          },
          {
            title: '车牌号',
            code: 'visitorCarInfor',
            type: 'text'
          },
          {
            title: '手机号',
            code: 'visitorPhoneNumber',
            type: 'text'
          },
          {
            title: '来访时间',
            code: 'beginTime',
            type: 'text'
          },
          {
            title: '结束时间',
            code: 'endTime',
            type: 'text'
          },
          {
            title: '来访事由',
            code: 'purpose',
            type: 'text'
          },
          {
            title: '上传头像',
            code: 'pic',
            type: 'imgArray'
          },
          {
            title: '上传资料',
            code: 'information',
            type: 'imgArray'
          },
        ]
      },
      {
        title: '同行人员信息',
        data: [],
        detailTitleList: [
          {
            title: '姓名',
            code: 'visitorName',
            type: 'text'
          },
          {
            title: '手机号',
            code: 'visitorPhoneNumber',
            type: 'text'
          },
        ]
      },
      {
        title: '同行车辆信息',
        data: [],
        detailTitleList: [
          {
            title: '车牌号',
            code: 'visitorCarInfor',
            type: 'text'
          },
        ]
      }

    ],
    approve: [
      // {
      //   result: '已通过',
      //   name: '刘烨',
      //   desc: '2025-07-17 10:19:32',
      //   opinion: '通过',
      //   color: "red"
      // }
    ],
    stage: 1
  }
])
onMounted(() => {

})
const approveResultList = ref([
  {
    value: '0',
    label: '待审核'
  },
  {
    value: '2',
    label: '通过'
  },
  {
    value: '3',
    label: '未通过'
  },
])
const getMainInfoDetails = async () => {
  await getMainInfoDetailsByIdAuth(queryId.value).then((res: any) => {
    console.log(res, 'res')
    detailData.value = JSON.parse(JSON.stringify(listData.value[0]))

    console.log(detailData.value.list[1], 'detailData.value.list[1]')
    detailData.value.list[1].data[0] = res.data



    let pic = [];
    if (res.data.imgPath1) {
      pic.push({
        url: res.data.imgPath1,
        name: '图片1'
      })
    }
    if (res.data.imgPath2) {
      pic.push({
        url: res.data.imgPath2,
        name: '图片1'
      })
    }
    if (res.data.imgPath3) {
      pic.push({
        url: res.data.imgPath3,
        name: '图片1'
      })
    }
    let information = [];
    if (res.data.filePath1) {
      information.push({
        url: res.data.filePath1,
        name: '图片1'
      })
    }
    if (res.data.filePath2) {
      information.push({
        url: res.data.filePath2,
        name: '图片1'
      })
    }
    if (res.data.filePath3) {
      information.push({
        url: res.data.filePath3,
        name: '图片1'
      })
    }
    detailData.value.list[1].data[0].pic = pic
    detailData.value.list[1].data[0].information = information
    detailData.value.list[0].data[0] = res.data
    if (res.data.vpersonInfoS && res.data.vpersonInfoS.length > 0) {
      detailData.value.list[2].data = res.data.vpersonInfoS
    } else {
      detailData.value.list[2] = {}
    }
    if (res.data.vcarInfoS && res.data.vcarInfoS.length > 0) {
      detailData.value.list[3].data = res.data.vcarInfoS
    } else {
      detailData.value.list[3] = {}
    }
    console.log(res.data.vpersonInfoS)
    if (res.data.vapprovalResultS && res.data.vapprovalResultS.length > 0) {
      detailData.value.approve = res.data.vapprovalResultS.sort((a: any, b: any) => a.approvalStepNo - b.approvalStepNo).map((item: any) => {
        return {
          resultCode: item.approvalResult,
          result: approveResultList.value.find(f => f.value === item.approvalResult + '') ? approveResultList.value.find(f => f.value === item.approvalResult + '')?.label : '',
          name: item.approverName,
          desc: item.approveTime,
          opinion: item.approvalOpinion,
          color: item.approvalResult === '3' ? 'red' : ''
        }
      })
      // 审批节点哪个亮 （待审核或未通过节点亮，全部通过时最后一个节点亮）
      detailData.value.approve.forEach((item: any, index: number) => {
        if (item.resultCode + '' === '0' || item.resultCode + '' === '3') {
          detailData.value.stage = index
          return
        }
        detailData.value.stage = index
      })
    }

    // detailData.value=res
  })
}
const queryId = ref<any>(null);
const source = ref<any>(null);
onLoad(async (query: any) => {
  if (query.id && query.source) {
    queryId.value = query.id
    await getMainInfoDetails()
    source.value = query.source
    showDetailList.value = true
  }

})
</script>
<style scoped lang="scss"></style>
