<template>
  <view>
    <headle></headle>
    <view class="address-edit-container">

      <!-- 表单区域 -->
      <scroll-view :scroll-y="true" class="form-container">
        <view class="form-group">
          <text class="label">拜访校区</text>
          <picker mode="selector" @change="handleChangeXQ" :range="xQList" range-key="name">
            <view class="picker">{{ formData.campus }}</view>
          </picker>
        </view>
        <view class="form-group">
          <text class="label">拜访楼宇</text>
          <picker mode="selector" @change="handleChangeLY" :range="lYList" range-key="name">
            <view class="picker">{{ formData.building }}</view>
          </picker>
        </view>
        <view class="form-group">
          <text class="label">拜访房间</text>
          <input style="height: 1.5rem" v-model="formData.roomNo" placeholder="请输入拜访房间"/>
        </view>
        <view class="form-group">
          <text class="label">拜访单位</text>
<!--          <picker mode="selector" @change="handleChangeDW" :range="dWList">-->
<!--            <view class="picker">{{ dWText }}</view>-->
<!--          </picker>-->
          <view style="height: 1.5rem;width: 100%" @click="openTkiTree">
            {{formData.svaReservedText2}}
          </view>
          <tki-tree v-if="dWList" ref="treeSelectRef" rangeKey="deptName" idKey="deptId" :range="dWList" @confirm="treeConfirm"
          ></tki-tree>
        </view>
        <view class="form-group">
          <switch v-model="isDefault" :checked="isDefault" @change="toggleDefault"/>
          <text class="switch-label">设为默认区域信息</text>
        </view>
      </scroll-view>

      <!-- 操作按钮 -->
      <view class="action-buttons">
        <button type="primary" @click="saveAddress">保存地址</button>
        <button type="default" @click="cancelEdit">取消</button>
      </view>
    </view>
  </view>

</template>
<script setup lang="ts">
import {ref, onMounted} from 'vue';
import Headle from "@/pages/components/headle.vue";
import {onLoad} from "@dcloudio/uni-app";
import {addAddressList, type AddressParams, getDeptList, getVisitaddress, updateVisitaddress} from '@/api/address'
import {type AreaParams, getAreaList} from "@/api/area";
import {handleTree} from "@/utils/util";
import TkiTree from "@/pages/components/tki-tree/tki-tree.vue";

const areaList = ref([] as AreaParams[])
const isDefault=ref(false)
onLoad(async (query: any) => {
  if(query.areaId){
    await getVisitaddress(query.areaId).then(res=>{
      if(res){
        formData.value=res.data
        isDefault.value=(res.data.isDefault+''==="1")
      }

    })
  }




})
const getDataList=async ()=>{
  await getDeptList({}).then(res=>{
    if(res){
      dWList.value=handleTree(res.data,'deptId','parentId','children')
      console.log(dWList.value)
    }

  })
  //获取区域列表
  await getAreaList().then((res: any) => {
    if (res) {
      areaList.value = res.data
      xQList.value = areaList.value.filter((e: AreaParams) => e.parentId === 0)
    }
  })
}
getDataList()
const formData = ref<AddressParams>({
  building: "",
  buildingId: 0,
  campus: "",
  campusId: 0,
  id: 0,
  isDefault: "0",
  jobNum: "",
  orgcode: "",
  roomNo: "",
  svaReservedText1: "",
  svaReservedText2: "",
  teacherName: ""
});


const xQList = ref<AreaParams[]>()
const xqText = ref<string>("")
const handleChangeXQ = (e: any) => {
  if (xQList.value) {
    formData.value.campusId = xQList.value[e.detail.value].id
    formData.value.campus = xQList.value[e.detail.value].name
    lYList.value= areaList.value.filter((f: AreaParams) => f.parentId === formData.value.campusId)
  }
}
const lYList = ref<AreaParams[]>()
const lYText = ref<string>("")
const handleChangeLY = (e: any) => {
  if (lYList.value) {
    formData.value.buildingId = lYList.value[e.detail.value].id
    formData.value.building = lYList.value[e.detail.value].name
  }
}
const dWList = ref<any[]>()
const dWText = ref<string>("")
const handleChangeDW = (e: any) => {
  dWText.value = dWList.value[e.detail.value]
}
const toggleDefault = (e: any) => {
  // formData.value.isDefault = e.detail.value;
  if(e.detail.value){
    formData.value.isDefault='1'
  }else {
    formData.value.isDefault='0'
  }
};

const saveAddress = () => {
  if(formData.value.id){
    updateVisitaddress(formData.value).then(res=>{
      if(res){
        uni.showToast({
          title: '保存成功',
          success: () => {
            uni.navigateBack()
          }
        });
      }
    })
  }else {
    addAddressList(formData.value).then(res=>{
      if(res){
        uni.showToast({
          title: '保存成功',
          success: () => {
            uni.navigateBack()
          }
        });
      }
    })
  }
};
const treeSelectRef=ref()
const openTkiTree=()=>{
  treeSelectRef.value._show()
}
const treeConfirm=(e)=>{
  formData.value.svaReservedText1=e[0].deptId
  formData.value.svaReservedText2=e[0].deptName
}
// 取消编辑
const cancelEdit = () => {
  uni.navigateBack();
};

// 页面加载时获取地址ID（如果是编辑模式）
onMounted(() => {

});

// 获取地址详情
const getAddressDetail = async (addressId: string) => {

};
</script>
<style scoped lang="scss">
.address-edit-container {
  padding: 20rpx;
  display: flex;
  flex-direction: column;
}

.form-container {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20rpx;
}

.form-group {
  background-color: #ffffff;
  border-radius: 16rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);

}

.label {
  display: block;
  font-weight: bold;
  margin-bottom: 10rpx;
  color: #333;
}

input,
textarea {
  width: 100%;
  padding: 10rpx;
  border: none;
  font-size: 28rpx;
  border-bottom: 1rpx solid #eee;
}

.picker {
  width: 100%;
  padding: 10rpx;
  font-size: 28rpx;
  color: #333;
  height: 1rem;
}

.switch-label {
  margin-left: 20rpx;
  vertical-align: middle;
}

.action-buttons {
  display: flex;
  gap: 20rpx;

  button {
    flex: 1;
  }
}
</style>
