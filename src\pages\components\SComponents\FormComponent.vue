<template>
  <wrapper-component>
    <view class="content">
      <uni-forms ref="formRef" :modelValue="formData" :rules="rules">
        <template v-for="(item,index) in props.inputData" :key="index">
          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='textD' && !item.value" style="display: flex;align-items: center;border-bottom: solid 1px #e5e5e5;">
            <input @change="handleChangeInput" style="width: 14rem;" v-model="formData[item.code]" :placeholder="item.placeholder"/>
          </uni-forms-item>
          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='text' && !item.value">
            <uni-easyinput  @change="handleChangeInput"style="width: 14rem;" v-model="formData[item.code]" :placeholder="item.placeholder"/>
          </uni-forms-item>
          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='yzm' && !item.value">
            <view style="display: flex;width: 14rem;">
              <uni-easyinput @change="handleChangeInput" v-model="formData[item.code]" :placeholder="item.placeholder"/>
              <button :disabled="showDjss"  size="mini" style="width:6rem;display: flex;align-items: center;justify-content: center" @click="getPhoneCode(item.phoneCode)">{{showDjss?djs:'获取验证码'  }}</button>
            </view>
          </uni-forms-item>
          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='text' && item.value">
            <uni-easyinput @change="handleChangeInput" style="width: 14rem;" :value="item.value" disabled :placeholder="item.placeholder"/>
          </uni-forms-item>
          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='car'">
<!--            <uni-easyinput @change="handleChangeInput"  @click="handleClickCar" style="width: 14rem;" v-model="formData[item.code]" :placeholder="item.placeholder"/>-->
            <view  style="width: auto;position: relative;overflow: hidden;flex: 1;line-height: 1;font-size: 14px;height: 35px;display: flex;box-sizing: border-box;flex-direction: row;align-items: center;border: 1px solid #dcdfe6;border-radius: 4px;padding-left: 10px;   ">
              <view v-if="formData[item.code]" style="display: flex;justify-content: space-between;width: 100%;padding-right: 5px" >
                <view @click="handleClickCar" style="width: 80%;display: flex;align-items: center;">{{formData[item.code]}}</view>
                <view >
<!--                  <uni-icons type="close" size="16" @click="formData[item.code]=''"></uni-icons>-->
                  <uni-icons  type="clear" :size="24" :color="'#c0c4cc'" @click="formData[item.code]=''"></uni-icons>
                </view>
              </view>
              <view v-else  @click="handleClickCar" style=" color: #999;font-size: 12px;width: 100%;">
                请输入车牌号码
              </view>
            </view>
            <mosowe-plate-number
                v-if="showCarJp"
                v-model="formData[item.code]"
                :popup="true"
                auto-focus
                @focus="setPageHidden(true)"
                @blur="setPageHidden(false)"
            ></mosowe-plate-number>
          </uni-forms-item>
          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='select'">
            <view style="width: 100%;">
              <uni-data-select @change="handleChangeInput"  style="width: 14rem;float: right" :placeholder="item.placeholder"
                               v-model="formData[item.code]" :localdata="item.options"/>
            </view>
          </uni-forms-item>

          <uni-forms-item :label="item.label" :name="item.code" label-width="6rem" v-if="item.type ==='date'">
            <view style="width: 100%;">
              <uni-datetime-picker @change="handleChangeInput" :type="item.dateType" style="width: 14rem;float: right"
                                   :placeholder="item.placeholder" v-model="formData[item.code]"
                                   :start="item.start && formData[item.start]?formData[item.start]:item.end?newDateStart:newDateEnd"
                                   :end="item.end && formData[item.end]?formData[item.end]:''"
              />
            </view>
          </uni-forms-item>
          <view class="input-wrapper" v-if="item.type ==='imgList'">
            <view class="i-title">
              {{ item.label }}
            </view>
            <view class="i-c-i">
              <uni-file-picker @uU="handleUpImgS($event,item.code)" :image-list="formData[item.code]" v-if="item" :limit="item.limit"  :title=" `最多选择${item.limit}张图片`"></uni-file-picker>
            </view>
          </view>
        </template>
      </uni-forms>

      <!--      <template v-for="(item,index) in props.inputData" :key="index">-->
      <!--        <view class="input-wrapper" v-if="item.type ==='text'">-->
      <!--          <view class="i-title">-->
      <!--            {{ item.label }} :-->
      <!--          </view>-->
      <!--          <view class="i-content">-->
      <!--            <input type="text" v-model="formData[item.code]" :placeholder="item.placeholder"></input>-->
      <!--          </view>-->
      <!--        </view>-->
      <!--        <view class="input-wrapper" v-if="item.type ==='select'">-->
      <!--          <view class="i-title">-->
      <!--            {{ item.label }} :-->
      <!--          </view>-->
      <!--          <view class="i-content">-->
      <!--            <uni-data-select label="性别" style="width: 8rem" placeholder="请选择性别" v-model="formData[item.code]"-->
      <!--                             :localdata="item.options"/>-->
      <!--          </view>-->
      <!--        </view>-->
      <!--        <view class="input-wrapper" v-if="item.type ==='date'">-->
      <!--          <view class="i-title">-->
      <!--            {{ item.label }} :-->
      <!--          </view>-->
      <!--          <view class="i-content">-->
      <!--            <uni-datetime-picker :type="item.dateType" placeholder="日期" :clear-icon="false" v-model="formData[item.code]"/>-->
      <!--          </view>-->
      <!--        </view>-->
      <!--      </template>-->

    </view>
<!--    <uni-popup :mask-click="false" ref="inputDialog" type="dialog">-->
<!--      <view style="background-color: white">-->
<!--        <uni-plate-input :plate="plateNo"></uni-plate-input>-->
<!--      </view>-->
<!--    </uni-popup>-->

  </wrapper-component>
</template>
<script setup lang="ts">
import uniDatetimePicker from "@/pages/components/uni-datetime-picker/uni-datetime-picker.vue";
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import {onMounted, ref, defineModel, watch, computed} from "vue";
import type {PropType} from "vue";
import UniDataSelect from "@/pages/components/uni-data-select/uni-data-select.vue";
import uniForms from "@/pages/components/uni-forms/uni-forms.vue";
import uniFormsItem from "@/pages/components/uni-forms-item/uni-forms-item.vue";
import uniEasyinput from "@/pages/components/uni-easyinput/uni-easyinput.vue";
import uniFilePicker from "@/pages/components/uni-file-picker/uni-file-picker.vue";
import UniPlateInput from "@/pages/components/uni-plate-input/uni-plate-input.vue";
import uniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import {onLoad} from "@dcloudio/uni-app";
import {timestampToTime} from "@/utils/disposeDate";
import {getVCode} from "@/api/wuser";
import mosowePlateNumber from "@/pages/components/mosowe-plate-number/mosowe-plate-number.vue";
import uniIcons from "@/pages/components/uni-icons/uni-icons.vue";

const pageHidden = ref(false);
const setPageHidden = (val: boolean) => {
  showCarJp.value=val
  pageHidden.value = val;
  if(!val){
    handleChangeInput()
  }
};
const showCarJp=ref(false)
const handleClickCar=()=>{
  showCarJp.value=true
}


const rules = ref<any>({});
const formRef=ref()
const formData = ref<any>({});
const emit = defineEmits(["submit"]);
const handleChangeInput=()=>{
  emit("submit",formData.value);
}

const props = defineProps({
  inputData: {
    type: Array as PropType<any[]>,
    default: () => []
  },
  formData:{
    type:Object,
    default:()=>({})
  },
  rules: {
    type: Object,
    default: () => ({})
  }
});
rules.value=props.rules
watch(() => props.rules,()=>{
  rules.value=props.rules
})
watch(() => props.formData,()=>{
  formData.value=props.formData
})
const djs=ref(60)
const showDjss=ref(false)
const getPhoneCode=async (n:string)=>{
  console.log(n)
  if(formData.value[n] && formData.value[n].length===11){
    await getVCode(formData.value[n]).then(res=>{
      console.log(res)
    })
    uni.showToast({
      title: '获取验证码成功',
      icon: 'success'
    });
    showDjss.value=true
    const timer =setInterval(()=>{
      if(djs.value>0){
        djs.value=djs.value-1
        return
      }else {
        window.clearInterval(timer)
        showDjss.value=false
        djs.value=60
      }

    },1000)
  }else {
    uni.showToast({
      title: '请正确输入手机号',
      icon: 'error'
    })
  }

}
const handleUpImgS=(res:string[],code:string)=>{
  formData.value[code]=res
  handleChangeInput()
}
// const plateNo=ref<string>('');
// const inputDialog = ref<any>();
onMounted(()=>{

})
const newDateEnd=computed(()=>{
  return timestampToTime(new Date().getTime()+(10*60*1000))
})
const newDateStart=computed(()=>{
  return timestampToTime(new Date().getTime())
})
const handleSubmitRules=async ()=>{
  let isValid;
  await formRef.value.validate().then((res:any)=>{
    // console.log('表单数据信息：', res);
    isValid=true
  }).catch((err:any) =>{
    // console.log('表单错误信息：', err);
    isValid=false
  })
  return isValid
}
  defineExpose({handleSubmitRules})
</script>
<style scoped lang="scss">
.content {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;


  .input-wrapper {
    width: 100%;
    margin-bottom: 1rem;
    display: flex;
    align-items: start;
    justify-content: space-between;
    flex-wrap: wrap;
    flex-direction: column;

    .i-title {
      font-size: 14px;
      color: #606266;
      width: 6rem;

    }
    .i-c-i{
      width: 100%;
      display: flex;
      justify-content: space-between;
    }

    .i-content {
      display: flex;
      align-items: center;
      justify-content: flex-end;
      width: 15rem;

      input {
        text-align: end;
      }
    }
  }
}
:deep( .uni-forms-item__content .uni-easyinput .is-disabled) {
  color: #414141 !important;
}
.content-clear-icon {
  padding: 0 5px;
}
</style>
