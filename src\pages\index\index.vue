<template>
  <view class="wrapper">
    <headle-components></headle-components>
    <view class="content-wrapper">
      <view v-for="(item, index) in dataList[0].children" :key="index"
        style="position: relative;width:8rem;height:8rem;padding: 0.5rem;margin-bottom: 0rem;display: flex;justify-content: center;flex-direction: column;">
        <view :class="{ 'click-item': showClickItem === index }" @click="handleNavigateToClick(item, index)"
          style="box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);border: 1px solid #e4e4e4;padding-top: 0.8rem;padding-bottom: 0.8rem;border-radius: 10px;background-color: rgb(248, 248, 248);">
          <view style="display: flex;justify-content: center;">
            <image :src="item.logo" style="width: 2.5rem;height: 2.5rem"></image>
          </view>
          <view style="margin-top: 1rem;display: flex;justify-content: center;">
            {{ item.title }}
          </view>
          <view v-if="item.count"
            style="color:white;position:absolute;top:25px;right:15px;width: 23px;height: 23px;background-color: red;border-radius: 50%;display: flex;align-items: center;justify-content: center">
            {{ item.count }}
          </view>
        </view>

      </view>
    </view>
    <uni-popup ref="popup" type="dialog">
      <view
        style="background-color: white; width: 15rem;height: auto;display: flex;align-items: center;justify-content: center;flex-wrap: wrap; flex-direction:column;padding:0.5rem 0">
        <!--        <view v-if="selectedLink==='ewm'" style="width: 100%;display: flex;justify-content: center">长按保存二维码</view>-->
        <view><button v-if="selectedLink === 'ewm'" size="mini" @click="saveQrCode">保存二维码</button></view>
        <uqrcode v-if="selectedLink === 'ewm'" ref="uqrcodeRef" canvas-id="qrcode" :value=qrUrl :options="{ margin: 10 }">
        </uqrcode>
        <view v-if="selectedLink === 'lj'" style="width: 100%;padding:0 1rem;">
          <view
            style="display: flex;margin-top: 1rem;margin-bottom: 0.5rem;font-weight: 600;justify-content: space-between;align-items: center ">
            <view>
              邀请链接
            </view>
            <view>
              <button size="mini" @click="handleCopy">复制</button>
            </view>
          </view>
          <view>
            <uni-easyinput type="textarea" autoHeight :value="qrUrl" placeholder="请输入内容"></uni-easyinput>
          </view>
        </view>
        <view v-if="selectedLink === 'yym'" style="width: 100%;padding:0 1rem;">
          <!--          <view style="display: flex;margin-top: 1rem;margin-bottom: 0.5rem;font-weight: 600 ">您的授权码为</view>-->
          <view
            style="display: flex;margin-top: 1rem;margin-bottom: 0.5rem;font-weight: 600;justify-content: space-between;align-items: center ">
            <view>
              您的授权码为
            </view>
            <view>
              <button size="mini" @click="handleCopyYYM">复制</button>
            </view>
          </view>
          <view
            style="background-color: #dfdfdf;color: #539af4;border-radius: 3px;height: 2rem;display: flex;align-items: center;justify-content: center;font-weight: 600">
            {{ yqm }}
          </view>
        </view>
        <view v-if="selectedLink === 'yym'" style="color: red;font-size: 14px;margin-top: 0.5rem;">*授权码有效时间为{{ URLETime
          }}分钟</view>
        <view v-if="selectedLink === 'lj'" style="color: red;font-size: 14px;margin-top: 0.5rem;">*链接有效时间为{{ URLETime
          }}分钟</view>
        <view v-if="selectedLink === 'ewm'" style="color: red;font-size: 14px;margin-top: 0.5rem;">*二维码有效时间为{{ URLETime
          }}分钟</view>
      </view>
    </uni-popup>
    <uni-popup ref="selectAreaPopup" type="dialog">
      <scroll-view scroll-y="true" style="width: 100%;max-height: 40rem" @scrolltolower="handleScrolltolower">
        <view
          style="background-color: white; width: 20rem;display: flex;align-items: center;justify-content: center;flex-wrap: wrap;">
          <view style="display: flex;width: 100%;justify-content: center;margin-top: 0.5rem;margin-bottom: 0.5rem">
            请选择来访区域信息
          </view>
          <view :class="{ 'list-item': true, 'selected-list-item': selectedArea === item.id }"
            v-for="(item, index) in areaList" :key="index" @click="handleSelectedArea(item)">
            <view class="list-item-o">
              <view class="list-item-t">
                拜访校区:
              </view>
              <view class="list-item-c">{{ item.campus }}</view>
            </view>
            <view class="list-item-o">
              <view class="list-item-t">
                拜访楼宇:
              </view>
              <view class="list-item-c">{{ item.building }}</view>
            </view>
            <view class="list-item-o">
              <view class="list-item-t">
                拜访房间:
              </view>
              <view class="list-item-c">{{ item.roomNo }}</view>
            </view>
            <view class="list-item-o">
              <view class="list-item-t">
                拜访单位:
              </view>
              <view class="list-item-c">{{ item.svaReservedText2 }}</view>
            </view>
          </view>
          <view style="width:100%;display: flex;justify-content: space-around;margin-top: 2rem;padding-bottom: 1rem">
            <button style="width: 5rem" @click="handleCloseS">
              取消
            </button>
            <button type="primary" style="width: 5rem" @click="handleEditSButton">
              确定
            </button>
          </view>
        </view>
      </scroll-view>
    </uni-popup>

    <uqrcode v-if="erSh" :hide="true" ref="uqrcodeRef" canvas-id="qrcode" :value=qrUrl :options="{ margin: 10 }">
    </uqrcode>
  </view>
</template>
<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import { onLoad, onShow } from "@dcloudio/uni-app";
import headleComponents from '@/pages/components/headle.vue'
import cache from "@/utils/cache";
import sp from "@/static/index/sp.png"
import sq from "@/static/index/sq.png"
import tx from "@/static/index/tx.png"
import yy from "@/static/index/yy.png"
import qc from "@/static/index/qc.png"
import area from "@/static/index/area.png"
import lj from "@/static/index/lj.png"
import yym from "@/static/index/yym.png"
import UniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import uqrcode from "@/pages/components/uqrcode/uqrcode/uqrcode.vue";
import UniEasyinput from "@/pages/components/uni-easyinput/uni-easyinput.vue";
import { getAddressList } from "@/api/address";
import { getYYM } from "@/api/api";
import { USER_INFO } from "@/enums/cacheEnums";
import { encrypt } from "@/utils/jsencrypt";
import { getMainInfoListByTeacher } from "@/api/maininfo";
import { getConfigKey } from "@/api/params";
const selectedLink = ref('')
const menuList = ref([])
const bui = ref(cache.get('baseUrlImg'))
const dataList = ref([
  {
    title: '常用功能',
    children: [
      {
        title: '二维码生成',
        logo: qc,
        code: 'ewm',
        component: '/pages/visitor/approve/index',
      },
      {
        title: '链接生成',
        logo: lj,
        code: 'lj',
        component: '/pages/visitor/approve/index',
      },
      {
        title: '授权码生成',
        logo: yym,
        code: 'yym',
        component: '/pages/visitor/approve/index',
      },
      {
        title: '审批管理',
        logo: sp,
        component: '/pages/visitor/approve/index',
        count: 0
      },
      {
        title: '访客申请管理',
        logo: sq,
        component: '/pages/visitor/applicationRecord/index',
      },
      {
        title: '访客通行记录',
        logo: tx,
        component: '/pages/visitor/visitoraccessrecord/index'
      },
      {
        title: '访客邀约',
        logo: yy,
        component: '/pages/visitor/invitation/index'
      },
      {
        title: '区域管理',
        logo: area,
        component: '/pages/visitor/areamanage/index'
      }
    ]
  }
] as any[])

const getOrderList = async () => {
  await getMainInfoListByTeacher({ jobNum: userInfo.value.userName }).then((res: any) => {
    if (res) {
      dataList.value[0].children[3].count = res.total
    }
  })
}
onMounted(() => {

})

const dataListRecently = ref([] as any[])

const getMenuList = () => {

}
onLoad(() => {

  userInfo.value = cache.get(USER_INFO)

})
const selectedMenu = ref<any>()
const uqrcodeRef = ref()
const qrUrl = ref('http://192.168.1.10:5177/#/pages/visitor/inSchoolApply/index')
const yqm = ref('')
const handleNavigateToClick = async (item: any, index: number) => {
  showClickItem.value = index
  setTimeout(() => { showClickItem.value = -1 }, 100)
  selectedMenu.value = item

  if (item.title.includes('生成') || item.title === '访客邀约') {
    selectedLink.value = item.code
    selectAreaPopup.value.open()
    // popup.value.open()
  } else {
    uni.navigateTo({
      // url: '/pages/linkweb/index?url=' + encodeURIComponent(item.component)
      url: item.component
    })
  }
}
const popup = ref()
const selectAreaPopup = ref()
const selectedArea = ref('');
const areaList = ref<any[]>([]);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10
})
const areaTotal = ref()
const getAreaList = async () => {
  // 这里应该是调用接口获取数据的逻辑
  await getAddressList(queryParams.value).then((res: any) => {
    if (res) {
      if (queryParams.value.pageNum === 1) {
        areaList.value = res.rows
      } else {
        areaList.value = [...areaList.value, ...res.rows]
      }
      areaTotal.value = res.total
    }
  })

  selectedArea.value = areaList.value ? areaList.value.find(item => item.isDefault === '1') ? areaList.value.find(item => item.isDefault === '1').id : '' : ''
};
const handleScrolltolower = () => {
  if (areaTotal.value > (queryParams.value.pageNum * queryParams.value.pageSize)) {
    queryParams.value.pageNum += 1
    getAreaList()
  }

}
const handleCloseS = async () => {
  selectAreaPopup.value.close()
}
const userInfo = ref<any>()
const erSh = ref(false)
const handleEditSButton = async () => {
  if (selectedMenu.value.title === '访客邀约') {
    selectAreaPopup.value.close()
    uni.navigateTo({
      url: '/pages/visitor/invitation/index?areaid=' + selectedArea.value
    })
  }
  // else if(selectedMenu.value.title==='授权码生成'){
  //   await getYYM(parseInt(selectedArea.value)).then((res:any)=>{
  //     if(res){
  //       yqm.value=res.msg
  //     }
  //   })
  //   selectAreaPopup.value.close()
  //   popup.value.open()
  // }
  else {
    await getYYM(parseInt(selectedArea.value)).then((res: any) => {
      if (res) {
        yqm.value = res.msg
      }
    })
    selectAreaPopup.value.close()
    console.log(selectedArea.value,'selectedArea.value')
    let areaId = btoa(encodeURIComponent(selectedArea.value))
    let jobNum = btoa(encodeURIComponent(userInfo.value.userName))
    let teacherName = btoa(encodeURIComponent(userInfo.value.nickName))
    let authCode = btoa(encodeURIComponent(yqm.value))
    erSh.value = false
    qrUrl.value = 'http://192.168.1.10:5177/#/pages/visitor/inSchoolApply/index?areaid=' + areaId + "&jobNum=" + jobNum + "&teacherName=" + teacherName + "&authCode=" + authCode
    if (selectedMenu.value.code == 'ewm') {
      erSh.value = true
      await nextTick()
      const uqrcodeInstance = uqrcodeRef.value;
      uni.showLoading({
        title: '二维码生成中...',
      })
      if (uqrcodeInstance) {
        // 调用 make 方法并使用回调
        uqrcodeInstance.make({
          success: () => {
            uni.hideLoading()
            // console.log('二维码生成成功');
            saveQrCode();
          },
          fail: (err) => {
            console.error('二维码生成失败:', err);
          }
        });
      }
      return
    }
    popup.value.open()
  }
}
const handleSelectedArea = (item: any) => {
  selectedArea.value = item.id
}

const showClickItem = ref(-1)
const handleCopy = () => {
  uni.setClipboardData({
    data: qrUrl.value, // 要复制的内容
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      });
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000
      });
    }
  });
};
const handleCopyYYM = () => {
  uni.setClipboardData({
    data: yqm.value, // 要复制的内容
    success: () => {
      uni.showToast({
        title: '复制成功',
        icon: 'none'
      });
    },
    fail: () => {
      uni.showToast({
        title: '复制失败',
        icon: 'none',
        duration: 2000
      });
    }
  });
};
const URLETime = ref()
const codeETime = ref()
const handleGetYXSJ = async () => {
  await getConfigKey("urlValidityPeriod").then(res => {
    if (res.msg) {
      URLETime.value = parseInt(res.msg)
    }
  })
  await getConfigKey("authCodeValidityPeriod").then(res => {
    if (res.msg) {
      codeETime.value = parseInt(res.msg)
    }
  })
}
handleGetYXSJ()

const saveQrCode = () => {
  const uqrcodeInstance = uqrcodeRef.value;

  if (uqrcodeInstance) {
    uqrcodeInstance.save({
      success: (res) => {
        // uni.showToast({ title: '保存成功' });
      },
      fail: (err) => {
        console.error('保存失败:', err);
        uni.showToast({ title: '保存失败', icon: 'none' });
      }
    });
  }
};

onShow(() => {
  getMenuList()
  getAreaList()
  getOrderList()
})
</script>
<style scoped lang="scss">
.wrapper {
  .content-wrapper {
    margin-top: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: row;
    flex-wrap: wrap;
  }

  .selected-list-item {
    border: 1px solid #539af4 !important;
    background-color: rgb(228, 228, 228) !important;
  }

  .list-item {
    width: 90%;
    border: 1px solid #c5c3c3;
    margin-bottom: 1rem;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    align-items: stretch;
    box-sizing: border-box;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: rgb(248, 248, 248);

    .list-item-o {
      display: flex;
      justify-content: space-between;

      .list-item-t {}

      .list-item-c {}
    }

    .list-item-o:not(:last-child) {
      margin-bottom: 0.3rem;
    }

    .bottom-button {
      width: 100%;
      display: flex;
      flex-direction: column-reverse;
      flex-wrap: wrap;
      align-content: flex-end;
      align-self: flex-end;
      //border-top: 1px solid #c5c3c3;
      padding-top: 0.5rem;
      margin-top: 0.5rem;
    }
  }
}

.click-item {
  background-color: #9f9f9f !important;
}
</style>
