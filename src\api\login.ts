import request from '@/utils/request/index'

// 登录方法
export function login(data:any) {
  return request.post({
    url: '/login',
    data: data
  })
}


// 获取用户详细信息
export function getInfo() {
  return request.get({
    url: '/getInfo'
  })
}

// 退出方法
export function logout() {
  return request.post({
    url: '/logout',
  })
}

// 获取验证码
export function getCodeImg() {
  return request.get({
    url: '/captchaImage',
    timeout: 20000
  })
}
