<template>
  <scroll-view
      ref="scrollView"
      :scroll-x="true"
      :scroll-left="scrollLeft"
      :scroll-with-animation="true"
      class="scroll-view"
  >
    <text v-for="(item, index) in messages" :key="index + messages.length" class="scroll-item">{{ item }}</text>

  </scroll-view>
</template>
<script setup lang="ts">
import { ref, onMounted, nextTick,onUnmounted } from 'vue';

const messages = [
  "界面顶部，提供展示访客入校须知消息的播报栏；99999999",
  "界面顶部，提供展示访客入校须知消息的播报栏；99999999",
  "界面顶部，提供展示访客入校须知消息的播报栏；99999999",
];

const scrollView = ref();
const scrollLeft = ref(0);
const duration = ref(10); // 控制滚动速度，越小越快
let animationFrameId = 0;

//获取内容宽度并启动滚动动画
const startScroll = () => {
  const query = uni.createSelectorQuery().in(scrollView.value);
  query.select('.scroll-view').boundingClientRect((res) => {
    if (!res) return;
    const contentWidth = res.width;
    animateScroll(contentWidth);
  }).exec();
};
let timeoutId: number | null = null;
// 动画滚动函数
const animateScroll = (contentWidth: number) => {
  let currentLeft = scrollLeft.value;
  currentLeft += 1;
  if (currentLeft >= contentWidth / 2) {
    currentLeft = 0;
  }
  scrollLeft.value = currentLeft;

  // 控制每 20ms 执行一次，相当于 ~50fps
  timeoutId = setTimeout(() => {
    animateScroll(contentWidth);
  }, 20);
};

onMounted(() => {
  startScroll();
});

// 页面隐藏时停止动画
onUnmounted(() => {
  if (timeoutId !== null) {
    clearTimeout(timeoutId);
  }
});
</script>
<style scoped>
.scroll-view {
  display: flex;
  align-items: center;
  height: 20px;
  width: 100%;
  overflow: hidden;
  white-space: nowrap;
}

.scroll-item {
  padding-right: 20px;
  font-size: 16px;
  color: #333;
  flex-shrink: 0;
  white-space: nowrap;
}
</style>
