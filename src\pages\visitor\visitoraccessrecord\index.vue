<template>
  <view class="wrapper">
    <headle></headle>
    <search-component style="margin-top: 1rem" @search-data="handleSearchClick" source="2"></search-component>
    <list-component :source="'3'" :title-list="titleListData" :list="listData"
      @select-item="handleDetailClick"></list-component>
  </view>
</template>
<script setup lang="ts">
import { ref, defineOptions, defineProps, defineEmits, defineModel } from 'vue'
import ListComponent from "@/pages/components/SComponents/ListComponent.vue";
import SearchComponent from "@/pages/components/SComponents/SearchComponent.vue";
import Headle from "@/pages/components/headle.vue";
import tp from "@/static/logo.png";
import cache from "@/utils/cache";
import { USER_INFO } from "@/enums/cacheEnums";
import {onReachBottom, onShow} from "@dcloudio/uni-app";
import { getApprovalWithPersonList } from "@/api/approval";
import { getDicts } from "@/api/params";
const listData = ref([
  {
    id: 1,
    name: '张毅',
    phone: '18156259568',
    sex: '女',
    time: '2025-07-10 16:23:24',
    reason: '开会',
    visitStatus: '访问中',
    applyStatus: '审核通过',
    dd: '逸夫楼',
    scene: [{
      url: tp,
      name: '图片1'
    },]
  },
  {
    id: 2,
    name: '王伟',
    phone: '17825698547',
    sex: '男',
    time: '2025-07-09 08:58:25',
    reason: '开会',
    visitStatus: '访问中',
    applyStatus: '审核通过',
    dd: '逸夫楼'
  }
] as any[])
const listTotal = ref(0)
const userInfo = ref<any>(cache.get(USER_INFO))
const titleListData = ref([

  {
    title: '姓名',
    type: 'text',
    code: 'visitorName'
  },
  {
    title: '手机号',
    type: 'text',
    code: 'visitorPhoneNumber'
  },
  {
    title: '来访事由',
    type: 'text',
    code: 'purpose'
  },
  {
    title: '入校时间',
    type: 'text',
    code: 'inTime'
  },
  // {
  //   title:'来访状态',
  //   type:'text',
  //   code:'visitStatus'
  // },
  {
    title: '到访区域',
    type: 'text',
    code: 'building'
  },
] as any[])

//用于详情
const titleDataListDetails = ref([
  {
    title: '姓名',
    code: 'name'
  },
  {
    title: '性别',
    code: 'sex'
  },
  {
    title: '手机号',
    code: 'phone'
  },
  {
    title: '来访时间',
    code: 'time'
  },
  {
    title: '来访事由',
    code: 'reason'
  },
] as any[])
const handleDetailClick = (e: any) => {
  uni.navigateTo({
    url: '/pages/visitor/generalPage/details?id=' + e.id + '&source=3',
    success(result) {
      // result.eventChannel.emit('detail',e)
      // result.eventChannel.emit('detailTitle',titleDataListDetails.value)
    },
  })
}
const handleSearchClick = (e: any) => {
  queryParams.value.visitorName = e.visitorName
  queryParams.value.inDate = e.inDate
  getAccessRecordList(queryParams.value)
}
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  inDate: null,
  visitorName: null,
  approverId: userInfo.value.userName
})
let getAccessRecordList = async (data: any) => {
  await getApprovalWithPersonList(data).then((res: any) => {
    if (res) {
      if (queryParams.value.pageNum === 1) {
        listData.value = []
        listData.value = res.rows
        listTotal.value = res.total
        console.log(res, 'res');
      } else {
        listData.value = [...listData.value, ...res.rows]
      }
      // 翻译来访事由
      const purposeList = ref([])
      getDicts("reason_for_visit").then(res => {
        if (res) {
          purposeList.value = res.data.map((m: any) => {
            return {
              text: m.dictLabel,
              value: m.dictValue
            }
          })
          listData.value.forEach(item => {
            item.purpose = purposeList.value.find(m => m.value === item.purpose).text
          })
        }
      })
      // console.log(listData.value, 'listData.value');
    }
  });
}

onShow(() => {
  getAccessRecordList(queryParams.value)
})
onReachBottom(()=>{
    if(listTotal.value>(queryParams.value.pageNum*queryParams.value.pageSize)){
    queryParams.value.pageNum+=1
    getAccessRecordList(queryParams.value)
  }
})

</script>
<style scoped lang="scss">
.wrapper {
  width: 100%;

  .search-wrapper {}
}
</style>
