<template>
	<view class="notice-main" :style="{background:props.background}">
		<view v-show="props.showIcon" class="notice-icon">
			<uni-icons type="sound" size="25" :color="props.color"></uni-icons>
		</view>
		<view class="notice-box">
			<swiper class="notice-item" circular disable-touch autoplay easing-function="linear"
				:vertical="props.vertical"
				:interval="props.interval"
				:duration="props.duration">
				<swiper-item v-for="(item,index) in props.textlist" :key="index">
					<text class="text" :style="{color:props.color}">{{item.title}}</text>
				</swiper-item>
			</swiper>
		</view>
		<view v-show="props.showMore"
		class="notice-more" :style="{color:props.color}">
			查看更多
		</view>
	</view>
</template>

<script setup>
import uniIcons from "@/pages/components/uni-icons/uni-icons.vue";
const props = defineProps({
  textlist:{
    type:Array
  },
  vertical:{
    type:Boolean,
    default:false
  },
  interval:{
    type:[Number, String],
    default:2000
  },
  duration:{
    type:[Number, String],
    default:2000
  },
  showIcon:{
    type:<PERSON>olean,
    default:false
  },showMore:{
    type:Boolean,
    default:false
  },color:{
    type:String,
    default:'#f19e07'
  },background: {
    type: String,
    default: '#f7eddf'
  },
})
defineOptions({name:'u-notice'})

</script>
<style lang="scss" scoped>
	$bg: #f5f5f5;
	$color: #333;
	.notice-main{
		background-color:$bg;
		padding:0 10rpx;
		display: flex;
		align-items: center;
    width: 100%;
    height: 30px;
	}
	.notice-icon{
		width: 70rpx;
		box-sizing: border-box;
		text-align: center;
	}
	.notice-more{
		box-sizing: border-box;
		width: 180rpx;
		font-size: 26rpx;
		color:$color;
		text-align: center;
	}
	.notice-box{
		width: 100%;
		box-sizing: border-box;
	}
	.notice-item{
		height: 100rpx;
	}
	.text{
		height:100%;
		color: $color;
		font-size:35rpx;
		display: flex;
		justify-content: center;
		align-items: center;
		font-weight: 500;
	}
</style>
