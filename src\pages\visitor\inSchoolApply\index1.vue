<template>
  <view class="wrapper" style="background-color: rgb(248, 248, 248)">
    <headle></headle>
    <wrapper-component >
      <view style="padding: 0 0.5rem">
        <view style="margin-top: 1rem;font-weight: 600">
          访客信息
        </view>
        <view>
          <form-component :input-data="inputData"></form-component>
        </view>
        <view style="margin-top: 1rem;font-weight: 600">
          同行人员
        </view>
        <view v-for="(item,index) in txInputData" :key="index">
          <form-component :input-data="item"></form-component>
          <view style="display: flex">
            <button v-if="index===txInputData.length-1" style="width: 6rem" size="mini" type="primary" @click="addTxInputData">
              添加
            </button>
            <button v-if="txInputData.length>1" style="width: 6rem" size="mini" @click="removeTxIn(index)">
              删除
            </button>
          </view>
        </view>
        <view style="margin-top: 1rem;font-weight: 600">
          拜访信息
        </view>
        <view>
          <form-component :input-data="bfInputData"></form-component>
        </view>
        <button type="primary" style="margin-bottom: 0.5rem" @click="handleSubButton">
          提交
        </button>
      </view>

    </wrapper-component>
    <uni-popup :mask-click="false" ref="xzDialog" type="dialog" title="入校须知">
      <view style="background-color: white;padding:1rem;width: 15rem">
        <view style="font-weight:600;display: flex;align-items: center;justify-content: center;margin-bottom: 0.5rem">
          入校须知
        </view>
        <view style="">
          <view style="text-indent: 2em;">禁止携带易燃易爆、管制刀具等危险物品入校。</view>
          <view style="text-indent: 2em;">校内禁止吸烟、酗酒、赌博或大声喧哗。</view>
        </view>
        <view style="display: flex;justify-content: flex-end;margin-top: 2rem" >
<!--          <button  style="width: 5rem" @click="handleJjDialog">-->
<!--            取消-->
<!--          </button>-->
          <button type="primary"  style="width: 5rem" @click="handleCloseDialog">
            确定
          </button>
<!--          <view style="height: 2rem;width: 50%; display: flex;align-items: center;justify-content: center;font-size: 18px">取消</view>-->
<!--          <view style="height: 2rem;width: 50%; display: flex;align-items: center;justify-content: center;font-size: 18px;background-color: #007AFF;color: white">确定</view>-->
        </view>
      </view>
    </uni-popup>
  </view>
</template>
<script setup lang="ts">
import formComponent from '@/pages/components/SComponents/FormComponent.vue'
import WrapperComponent from "@/pages/components/SComponents/WrapperComponent.vue";
import {onMounted, ref} from "vue";
import uniPopup from "@/pages/components/popup/uni-popup/uni-popup.vue";
import Headle from "@/pages/components/headle.vue";
const inputData = ref([
  {
    label: '姓名',
    type: 'text',
    placeholder: '请输入姓名',
    code: 'name',
  },
  {
    label: '证件号码',
    type: 'text',
    placeholder: '请输入证件号码',
    code: 'num'
  },
  {
    label: '手机号',
    type: 'text',
    placeholder: '请输入手机号',
    code: 'phone'
  },
  {
    label: '来访缘由',
    type: 'select',
    placeholder: '请输入来访缘由',
    code: 'lfyy',
    value:'开会',
    options: [
      {
        text: '开会',
        value: '1'
      },
      {
        text: '访友',
        value: '2'
      }
    ]
  },
  {
    label: '车牌号',
    type: 'text',
    placeholder: '请输入车牌号',
    code: 'car'
  },
  {
    label: '性别',
    type: 'select',
    placeholder: '请选择性别',
    code: 'sex',
    options: [
      {
        text: '男',
        value: '1'
      },
      {
        text: '女',
        value: '2'
      }
    ]
  },
  {
    label: '开始时间',
    type: 'date',
    placeholder: '请选择开始时间',
    code: 'startTime'
  },
  {
    label: '结束时间',
    type: 'date',
    placeholder: '请选择结束时间',
    code: 'endTime'
  },
  {
    label: "相关证明资料",
    type: 'imgList',
    placeholder: '请上传图片',
    code: 'img',
    limit:3
  },
])
const txInputData = ref<any[]>([])
const addTxInputData=()=>{
  txInputData.value.push([
    {
      label: '姓名',
      type: 'text',
      placeholder: '请输入姓名',
      code: 'name',
    },
    {
      label: '证件号码',
      type: 'text',
      placeholder: '请输入证件号码',
      code: 'num'
    },
    {
      label: '手机号',
      type: 'text',
      placeholder: '请输入手机号',
      code: 'phone'
    },
    {
      label: '性别',
      type: 'select',
      placeholder: '请选择性别',
      code: 'sex',
      options: [
        {
          text: '男',
          value: '1'
        },
        {
          text: '女',
          value: '2'
        }
      ]
    },
    {
      label: "日期",
      type: 'date',
      placeholder: '请选择时间',
      code: 'time',
      dateType:'date'
    },
    {
      label: "相关证明资料",
      type: 'imgList',
      placeholder: '请上传图片',
      code: 'img',
      limit:3
    },
  ])
}
addTxInputData()
const removeTxIn=(index)=>{
  txInputData.value.splice(index,1)
}
const bfInputData=ref([
  {
    label: '被拜访人工号',
    type: 'text',
    placeholder: '请输入被拜访人工号',
    code: 'gh',
    value:'SD66691'
  },
  {
    label: '被拜访人姓名',
    type: 'text',
    placeholder: '请输入被拜访人姓名',
    code: 'xm',
    value:'刘烨'
  },

  {
    label: '拜访校区',
    type: 'text',
    placeholder: '请输入拜访校区',
    code: 'xq',
    value:'东校区'
  },
  {
    label: '拜访楼宇',
    type: 'text',
    placeholder: '请输入拜访楼宇',
    code: 'ly',
    value:'逸夫楼'
  },
  {
    label: '拜访房间',
    type: 'text',
    placeholder: '请输入拜访房间',
    code: 'fj',
    value:'2208'
  },
  {
    label: '拜访单位',
    type: 'text',
    placeholder: '请输入拜访单位',
    code: 'dw',
    value:'保卫科'
  },
])
onMounted(()=>{
  xzDialog.value.open();
})
const xzDialog=ref()
const handleCloseDialog=()=>{
  xzDialog.value.close();
}
const handleJjDialog=()=>{
  window.close()
}
const handleSubButton=()=>{
  uni.showToast({
    title:'提交成功',
    icon:'success'
  })
  uni.reLaunch({
    url:'/pages/visitor/generalPage/details?id='+1+'&source=4'
  })
}
</script>
<style scoped lang="scss">

</style>
