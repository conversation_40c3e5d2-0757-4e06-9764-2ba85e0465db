<template>
  <main-wrapper style="background-image: url('../../static/banner.jpg');background-size: 100% 100%">
    <view class="content-wrapper">
      <view class="content">
        <view class="content-title">
          出入校管理系统
        </view>
        <!--      <input placeholder="账号" >-->
        <!--      <input placeholder="密码">-->
        <!--      <button type="primary" @click="handelSub">登录</button>-->
        <div>
          请通过cas登录
        </div>
      </view>
    </view>
  </main-wrapper>
</template>

<script setup lang="ts">
import mainWrapper from '@/pages/components/mainWrapper.vue';
import {reactive} from "vue";
import {decrypt,encrypt} from "@/utils/jsencrypt";
import type {doLoginParams} from "@/api/casLogin";
import {loginByStudentCode,loginByTeacherCode} from "@/api/casLogin";
import cache from "@/utils/cache";
import {TOKEN_KEY} from "@/enums/cacheEnums";

const loginParams=reactive({
  data:{

  } as doLoginParams
})
const jLoginParams=reactive({
  data:{

  } as doLoginParams
})

const doLogin=async ()=>{
  let url=location.href
  let subUrl=url.indexOf('?')>1?url.substring(url.indexOf('?')+1).split('&'):[]
  subUrl.forEach(e=>{
    let key=e.substring(0,e.indexOf('='))
    let value=e.substring(e.indexOf('=')+1)
    //@ts-ignore
    loginParams.data[key]=value
    //@ts-ignore
    jLoginParams.data[key]=decrypt(decodeURIComponent(atob(decodeURIComponent(value))))
  })
  await uni.showLoading({ title: '登录中' });
  try {
    if(jLoginParams.data.idType && jLoginParams.data.idType==='1'){
      await loginByStudentCode({ username: jLoginParams.data.loginName, secretKey: decodeURIComponent(loginParams.data.loginName),thisTime:'0' }).then(res => {
        console.log(res.code)
        if(res.code==200){
          cache.set(TOKEN_KEY,res.token.access_token)
          cache.set('idType',jLoginParams.data.idType)
          uni.reLaunch({
            url:'/pages/index/index'
          })
        }
      }).catch(err => {
        console.log(err)
      })
    }
    else if(jLoginParams.data.idType && jLoginParams.data.idType==='3'){
      await loginByTeacherCode({ username: jLoginParams.data.loginName, secretKey: decodeURIComponent(loginParams.data.loginName),thisTime:'0' }).then(res => {
        if(res.code==200){
          cache.set(TOKEN_KEY,res.token.access_token)
          cache.set('idType',jLoginParams.data.idType)
          uni.reLaunch({
            url:'/pages/index/index'
          })
        }
      }).catch(err => {

      })
    }
  }catch (e){
    console.log('报错')
    console.log(e)
  } finally {
    uni.hideLoading()
  }
  uni.hideLoading()
}
doLogin()
console.log(jLoginParams.data)
const handelSub = () => {
  uni.reLaunch({
    url:'/pages/index/index'
  })
}

</script>

<style scoped lang="scss">
.content-wrapper{
  width: 100%;
  .content{
    display: flex;
    margin-top: 12rem;
    width: 100%;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    color: white;
    input {
      background-color: white;
      width: 80%;
      margin-bottom:1rem;
      height: 2rem;
      padding-left: 1rem;
      border-radius: 1rem;
    }
    button{
      width: 50%;
      border-radius: 1.5rem;
      margin-top:1rem;
    }
    .content-title{
      font-weight: 600;
      font-size: 32px;
      margin-bottom: 2rem;
    }
  }
}
</style>
